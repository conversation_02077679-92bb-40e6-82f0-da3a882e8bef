import sys
sys.path.append('./chatcode')
from askdoc.websearch.tavily_websearch import TavilyWebSearchModule, TavilyClient

import json

def main():
    tavily_search =TavilyWebSearchModule(
        api_key='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'
    )

    result = tavily_search.websearch(
        query="皮革水解物的危害是什么",
    )

    return result

if __name__ == '__main__':
    print(f'{json.dumps(main(), indent=4, ensure_ascii=False)}')