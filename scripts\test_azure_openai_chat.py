'''测试key,base'''
import openai
openai.api_type = "azure"
openai.api_key = "********************************"
openai.api_base = "https://af-southindia-aoai.openai.azure.com/"
openai.api_version = "2024-02-01"

openai.api_key = "********************************"
openai.api_base = "https://af-eastus-aoai.openai.azure.com/"
openai.api_version ="2024-02-01"

response = openai.ChatCompletion.create(
    engine="AF-gpt4o", # engine = "deployment_name".
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Does Azure OpenAI support customer managed keys?"},
        {"role": "assistant", "content": "Yes, customer managed keys are supported by Azure OpenAI."},
        {"role": "user", "content": "Do other Azure AI services support this too?"}
    ]
)

print(response['choices'][0]['message']['content'])