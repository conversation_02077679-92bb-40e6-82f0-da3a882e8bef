import os
import openai
from dotenv import load_dotenv

env_path = os.path.join(os.path.dirname(__file__), '.env')
load_dotenv(env_path)
service_endpoint = os.getenv("service_endpoint")
index_name = "test001"
# azure_key = os.getenv("key", "y69GPCEixQ0r68gckvRHufxlxUf6vv6TCgyRuAtxWxAzSeD6UyKe")
azure_key = "y69GPCEixQ0r68gckvRHufxlxUf6vv6TCgyRuAtxWxAzSeD6UyKe"
openai.api_type = os.getenv("openai.api_type")
openai.api_key =  os.getenv("openai.api_key")
openai.api_base =  os.getenv("openai.api_base")
openai.api_version = os.getenv("openai.api_version")

