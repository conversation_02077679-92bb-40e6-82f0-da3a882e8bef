import os
import logging
import sys
sys.path.append('/home/<USER>/jack/chatbot-alg/chatcode')

import tiktoken

logger=logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
import random
import string
import json


from askdoc.utils.commonUtils import identify_file_type,excel_to_markdown



def generate_random_string(length=26):
    characters = string.ascii_lowercase
    random_string = ''.join(random.choice(characters) for _ in range(length))


    # timestamp = time.time()
    random_string_with_timestamp = random_string
    return random_string_with_timestamp


def save_to_json(data, json_file_path):
    # 打印出文件保存路径
    print(f"Saving data to: {json_file_path}")
    os.makedirs(os.path.dirname(json_file_path), exist_ok=True)
    with open(json_file_path, 'w', encoding='utf-8') as json_file:
        json.dump(data, json_file, ensure_ascii=False, indent=4)


def num_tokens_from_messages_excel(messages):
    encoding = tiktoken.get_encoding("cl100k_base")
    num_tokens = 0
    if type(messages) == str:
        num_tokens += 4  # 每条消息的开头和结尾
        num_tokens += len(encoding.encode(messages))
    else: 
        for message in messages:
            num_tokens += 4  # 每条消息的开头和结尾
            num_tokens += len(encoding.encode(message))
    num_tokens += 2  # 每个回复以<im_start>assistant开始
    return num_tokens

def read_excel2markdown(file_path, filename='1', ct='0', et='0', fileid='1', indexName=111,folder_id='test1'):
    if(indexName==111):
        indexName = generate_random_string()
    max_tokens=512
    output_dir = "data"

    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} does not exist.")
        return

    # 检查并创建保存目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    markdown_content = excel_to_markdown(file_path)
    print(f"markdowm content is: {markdown_content}")

    pages_result = []

    # 遍历每个 sheet 的内容
    for sheet_name, content in markdown_content.items():
        temp_text = ""
        split_list = content.split('\n')

        if  not split_list:
            continue
            
        # 第一行是标题行
        headers = split_list[0]
        datarows = split_list[1:]
        # 开始包含标题行
        temp_text = headers + '\n' 

        # 按字符遍历内容
        for row in datarows:
            temp_text += row +'\n'

            # 如果超过最大字符长度
            if num_tokens_from_messages_excel(temp_text) >= max_tokens:
                pages_result.append({
                    'id': generate_random_string(),
                    'content': temp_text.strip(),
                    'tag': f"来源：{filename} (Sheet: {sheet_name})",  # 添加 sheet 名信息
                    'filename': filename,
                    'starttime': ct,
                    'endtime': et,
                    'idfile': fileid,
                    'folder_id': folder_id,
                    'filetype': identify_file_type(filename)
                })
                temp_text = headers + '\n'   # 重新开始新块

        # 处理剩余未分割的文本
        if temp_text.strip():
            pages_result.append({
                'id': generate_random_string(),
                'content': temp_text.strip(),
                'tag': f"来源：{filename} (Sheet: {sheet_name})",  # 添加 sheet 名信息
                'filename': filename,
                'starttime': ct,
                'endtime': et,
                'idfile': fileid,
                'folder_id': folder_id,
                'filetype': identify_file_type(filename)
            })

    # 保存结果到 JSON 文件
    save_to_json(pages_result, f"data/jsonfile_{indexName}.json")
    print(f"IndexName is {indexName}")
    return indexName


index_name = read_excel2markdown("/home/<USER>/jack/chatbot-alg/scripts/嵌入测试/Matrix2.xlsx", filename='1', ct='0', et='0', fileid='1', indexName=111,folder_id='test1')
