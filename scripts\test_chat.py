"""
this is a test script for chat completion
"""
import openai
openai.api_type = "azure"
openai.api_key= '********************************' 
openai.api_base = 'https://af-eastus-aoai.openai.azure.com/' 
openai.api_version ="2024-02-01"

def filter_filename(filenamelist:list[dict]):
    """让gpt筛选文件名"""

    supposed_format='''
    {"selected_document": [fileid1, fileid2,]}
    '''

    response_format={ "type": "json_object" }
    messages=[
        {'role': 'system',
        'content': ('请你扮演一个文档筛选机器人，用户会给你一个请求和搜索到的文档的名字，'
                    '请你筛选出最有可能包含答案的文档.你的回答应以json格式给出.json格式'
                    f'为：{supposed_format}. for example: '
                    '问题：乳粉的感官要求是什么？ 待过滤文档：[{"filename":"乳粉国家标准"，"fileid":"12258"}'
                    ',{"filename":"乳粉中污染物含量要求.pdf","fileid":"27736"}].你应输出：'
                    '{"selected_document": [12322]}.文档的数量没有限制'
                    )
        },
        {'role': 'user', 'content': f'请求：按照GB2762食品中铅的限量 是多少? 搜索到的文档的名字： {filenamelist}'},
    ]


    response=openai.ChatCompletion.create(
        engine='4o-mini',
        messages= messages,
        response_format=response_format,
    )


    print(response.choices[0].message.content)


if __name__ == '__main__':

    filenamelist=[
        {'filename': 'GB 2762-2022 食品安全国家标准 食品中污染物限量.pdf', 'fileid': 12344},
        {'filename': 'DB53-T 288-2009 食品中铅等18种元素的测定.pdf', 'fileid': 35456},
        {'filename': '商务部出口商品技术指南(食品污染物、农残限量).pdf', 'fileid': 239847},
        {'filename': '欧盟对于干辣椒中铅限量是多少？', 'fileid': 87435},
        {'filename': '印尼干裙带菜铅含量限值？', 'fileid': 23954},
        {'filename': '澳大利亚对芝麻的铅镉限量', 'fileid': 2954}
    ]

    filter_filename(filenamelist)
