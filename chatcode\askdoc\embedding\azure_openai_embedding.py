from typing import <PERSON><PERSON>,List,Union,Dict
from .embedding import embedding

import openai

class AzureOpenAIEmbeddingModule(embedding):

    def __init__(
            self,
            api_key: str,
            api_base: str,
            api_version: str,
    ):
        self.api_key = api_key
        self.api_base = api_base
        self.api_version = api_version

    def generate_embedding(
            self,
            query_or_querylist: Union[str, List],
            engine: str,
    ) -> Tuple[List,str,int]:
        openai.api_type = "azure"
        openai.api_key = self.api_key
        openai.api_base = self.api_base
        openai.api_version = self.api_version

        embedding_result=[]
        embedding_model=""
        embedding_token_usage=0
        for query in query_or_querylist:
            response = openai.Embedding.create(
                input=query,
                engine=engine
            )

            embedding_result.append(response['data'][0]['embedding'])
            embedding_model=response['model']
            embedding_token_usage+=response['usage']['total_tokens']

        return embedding_result,embedding_model,embedding_token_usage
