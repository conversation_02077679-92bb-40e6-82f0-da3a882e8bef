"""
1. 连接数据库，查询
2. 删除
3. 修改数据库
"""

import mysql.connector
from mysql.connector import Error

db_config = {
    "host": "sgsdbcne2flexiblemysqlafkmsprod.mysql.database.chinacloudapi.cn",
    "user": "sgs_kms_user",
    "pwd": "SH128_pkj45_J25gY_Kj12d",
    "db": "aichabot"
}

def get_database_connection():
    """建立数据库连接"""
    try:
        connection = mysql.connector.connect(
            host=db_config['host'],
            port=3306,
            user=db_config['user'],
            password=db_config['pwd'],
            database=db_config['db'],
            ssl_disabled=True,
            use_unicode=True,
            charset='utf8'
        )
        if connection.is_connected():
            return connection
    except Error as e:
        print("Error while connecting to MySQL", e)
        return None

def query_dev_corpus():
    """查询 dev_corpus 表中 filename 含有 '已废除' 的 fileid 和 filename 字段"""
    try:
        connection = get_database_connection()
        if connection is None:
            return []
        
        cursor = connection.cursor()
        query = "SELECT ID, NAME FROM dev_corpus WHERE filename LIKE %s"
        cursor.execute(query, ('%已废除%',))
        results = cursor.fetchall()
        return results
    except Error as e:
        print("Failed to query the table:", e)
        return []
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def save_to_txt(file_path, data):
    """将查询结果保存到txt文件中"""
    with open(file_path, 'w', encoding='utf-8') as file:
        for fileid, filename in data:
            file.write(f"{fileid}|{filename}\n")

def read_from_txt(file_path):
    """从txt文件中读取数据并保存到list中"""
    data = []
    with open(file_path, 'r', encoding='utf-8') as file:
        for line in file:
            fileid, filename = line.strip().split('|')
            data.append((fileid, filename))
    return data

# 主程序
if __name__ == "__main__":
    # 查询数据库并获取结果
    results = query_dev_corpus()

    # 保存结果到txt文件
    txt_file_path = 'output.txt'
    save_to_txt(txt_file_path, results)

    # 从txt文件中读取数据
    data = read_from_txt(txt_file_path)

    # 打印读取的数据
    print(data)
