#!/usr/bin/python3
# -*- coding: utf-8 -*-
PROD_ENV=1

import os
import sys
import logging
import traceback
import json

import requests
import time
import schedule
from lxml import etree

# 传输到askdoc的上一级目录下
sys.path.append('/mnt/datadisk1/deployment/chatbot')
sys.path.append(os.path.dirname(__file__)+"/..")
from pdf_processor import process_pdf
from pdf_processor import process_excel_pdf
from pdf_processor import process_ppt_pdf
from pdf_processor import process_image_pdf
from pdf_processor import process_csv_pdf
from pdf_processor import process_txt_pdf
from docx2pdf import convert
from database_ops import update_corpus_status
from askdoc.utils.fileUtils import print_log

logger=logging.getLogger(name=__name__)
logger.setLevel(logging.DEBUG)

module_dir = os.path.dirname(os.path.abspath(__file__))
config_file_path = os.path.join(module_dir,'config.json')
with open(config_file_path, 'r',encoding='utf-8') as f:
    config_data = json.load(f)["requestsfile_config"]

params = config_data['params']
local_filename = config_data['local_filename']
loginurl = config_data['loginurl']
loginparams = config_data['loginparams']
orgsurl = config_data['orgsurl']
orgsparams = config_data['orgsparams']
filename_url = config_data['filename_url']
filename_params = config_data['filename_params']
xml_content = config_data['xml_content']
download_url = config_data['download_url']
download_params = config_data['download_params']
download_directory = config_data['download_directory']


def get_token(loginurl, loginparams):
    """
    获取token
    :param loginurl:
    :param loginparams:
    :return:
    """
    # 发送HTTP GET请求，带上参数
    logger.debug(f'获取登陆token')
    response = requests.post(loginurl, params=loginparams)
    # 检查是否成功获取文件
    if response.status_code == 200:
        # 解码返回的内容
        decoded_content = response.content.decode()

        # 修复XML中的非法标签名
        fixed_content = decoded_content.replace('<q&a>', '<qa>').replace('</q&a>', '</qa>')

        # 解析修复后的XML内容
        root = etree.fromstring(fixed_content)

        # 查找<token>标签
        token_element = root.find('.//token')

        # 获取<token>标签的内容
        token_content = token_element.text if token_element is not None else None

        logger.debug(f'token:{token_content}')
        return (token_content)  # 输出token内容
    else:
        print_log(f"HTTP请求失败，状态码：{response.status_code}")


def get_orgs(orgsurl, orgsparams):
    """
    获取部门
    :param orgsurl:
    :param orgsparams:
    :return:
    """
    # 发送HTTP GET请求，带上参数
    logger.debug(f'获取部门,orgsurl:{orgsurl},orgsparams:{orgsparams}')
    response = requests.post(orgsurl, params=orgsparams)
    # 检查是否成功获取文件
    if response.status_code == 200:
        # 解码返回的内容
        decoded_content = response.content.decode()
        # 修复XML中的非法标签名
        fixed_content = decoded_content.replace('<q&a>', '<qa>').replace('</q&a>', '</qa>')

        # 解析修复后的XML内容
        root = etree.fromstring(fixed_content)

        orgs_element = root.find('.//ORG_ID')

        orgs = orgs_element.text if orgs_element is not None else None

        logger.debug(f'orgs:{orgs}')
        return orgs  # 输出token内容
    else:
        print(f"HTTP请求失败，状态码：{response.status_code}")


def get_file_info(filename_url, filename_params):
    """
    :param filename_url:
    :param filename_params:
    :return:
    """
    # print_log(filename_url, filename_params)
    # 发送HTTP GET请求，带上参数
    logger.debug(f'获取所有待下载文件元信息,参数：filename_url:{filename_url},filename_params:{filename_params}')
    response = requests.post(filename_url, params=filename_params)
    # 检查是否成功获取文件
    if response.status_code == 200:
        # 解码返回的内容
        decoded_content = response.content.decode()
        # 修复XML中的非法标签名
        fixed_content = decoded_content.replace('<q&a>', '<qa>').replace('</q&a>', '</qa>')
        # logger.debug(f'decoded_content:{decoded_content}')
        # 解析修复后的XML内容
        # root = etree.fromstring(fixed_content)
        return fixed_content
    else:
        print_log(f"HTTP请求失败，状态码：{response.status_code}")


def get_corpus_ids(xml_content):
    """
    获取语料id
    :param xml_content:
    :return:
    """
    root = etree.fromstring(xml_content)

    corpus_ids_element = root.find('.//CORPUS_IDS')
    corpus_ids = corpus_ids_element.text if corpus_ids_element is not None else None

    return (corpus_ids)  # 输出token内容


def parse_xml_and_download(xml_content, download_url, download_params, download_directory):
    """
    解析xml，并下载
    :param xml_content:
    :param download_url:
    :param download_params:
    :param download_directory:
    :return:
    """
    logger.debug('解析元信息并下载文件')
    root = etree.fromstring(xml_content)
    logger.debug(f'download_dir: {download_directory},download_url: {download_url},download_params: {download_params}')

    files_info = []
    for page_data in root.xpath('.//pageData'):
        logger.debug(f'')
        file_info = {
            'CREATE_TIME': page_data.findtext('CREATE_TIME'),
            'EXPIRE_TIME': page_data.findtext('EXPIRE_TIME', default=''),
            'ID': page_data.findtext('ID'),
            'NAME': page_data.findtext('NAME'),
            'CATEGORY': page_data.findtext('CATEGORY')
        }
        logger.debug(f'CREATE_TIME:{file_info["CREATE_TIME"]}')
        logger.debug(f'EXPIRE_TIME:{file_info["EXPIRE_TIME"]}')
        logger.debug(f'ID:{file_info["ID"]}')
        logger.debug(f'NAME:{file_info["NAME"]}')
        logger.debug(f'CATEGORY:{file_info["CATEGORY"]}')
        logger.debug(f'')

        download_params['FILE_ID'] = file_info['ID']
        download_file_url = download_url
        download_file_params = download_params

        if file_info['NAME']:
            local_filename = os.path.join(download_directory, file_info['NAME'])

            # 确保目录存在
            os.makedirs(os.path.dirname(local_filename), exist_ok=True)

            # 下载文件
            download_file(download_file_url, download_file_params, local_filename)

            file_info['FILE_PATH'] = local_filename
            files_info.append(file_info)

    return files_info


def download_file(url, params, local_filename):
    """
    下载文件
    :param url:
    :param params:
    :param local_filename:
    :return:
    """
    response = requests.get(url, params=params, stream=True)
    os.makedirs(os.path.dirname(local_filename), exist_ok=True)  # 确保目录存在
    if response.status_code == 200:
        with open(local_filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=1024):
                f.write(chunk)
    else:
        print_log(f"HTTP请求失败，状态码：{response.status_code}")


def main():
    """
    以下内容为接收文件进行embedding,并更新接口文件参数状态
    :return:
    """
    # print_log('执行 requestsfile.py 中的 main 方法')
    token = get_token(loginurl, loginparams)

    orgsparams["token"] = token

    orgs = get_orgs(orgsurl, orgsparams)
    
    filename_params["token"] = token
    filename_params["ORG_ID"] = orgs
    download_params["token"] = token
    download_params["ORG_ID"] = orgs
    xml_content = get_file_info(filename_url, filename_params)

    files_infos = parse_xml_and_download(xml_content, download_url, download_params, download_directory)
    # print(files_infos)
    for file_info in files_infos:
        try:
            file_path = file_info['FILE_PATH']
            file_name=file_info['NAME']
            file_id=file_info['ID']
            creat_time=file_info['CREATE_TIME']
            end_time=file_info['EXPIRE_TIME']
            folder_id=file_info['CATEGORY']
            file_extension = file_path.split('.')[-1].lower()
            # print(file_name)
            update_corpus_status(ID = file_id, EMBEDDING_STATUS=2)
            if file_extension == 'pdf':
                process_pdf(file_path,file_name, creat_time,end_time,file_id,folder_id=folder_id)
                update_corpus_status(ID = file_id, EMBEDDING_STATUS=1)
            elif file_extension in ['xlsx', 'xls']:
                process_excel_pdf(file_path,file_name, creat_time,end_time,file_id,folder_id=folder_id)
                update_corpus_status(ID = file_id, EMBEDDING_STATUS=1)
            elif file_extension == 'pptx':
                process_ppt_pdf(file_path,file_name,creat_time,end_time, file_id,folder_id=folder_id)
                update_corpus_status(ID = file_id, EMBEDDING_STATUS=1)
            elif file_extension in ['png', 'jpg', 'jpeg']:
                process_image_pdf(file_path,file_name,creat_time,end_time, file_id,folder_id=folder_id)
                update_corpus_status(ID = file_id, EMBEDDING_STATUS=1)
            elif file_extension in ['csv']:
                process_csv_pdf(file_path,file_name,creat_time,end_time, file_id,folder_id=folder_id)
                update_corpus_status(ID = file_id, EMBEDDING_STATUS=1)
            elif file_extension in ['txt']:
                process_txt_pdf(file_path,file_name,creat_time,end_time, file_id,folder_id=folder_id)
                update_corpus_status(ID = file_id, EMBEDDING_STATUS=1)
            elif file_extension in ['doc','docx']:
                temp_path='/mnt/datadisk1/deployment/chatbot/pdfs'
                pdf_path=os.path.join(temp_path, '.'.join(file_name.split('.')[0:-1]) + '.pdf')
                if sys.platform == 'linux':
                    logger.debug(f'linux系统, 开始使用libreoffice进行转换')
                    result=os.system(f'libreoffice24.2 --headless --convert-to pdf --outdir \'{temp_path}\' \'{file_path}\'')
                    if result == 0:
                        logger.debug(f'libreoffice转换成功')
                    else:
                        logger.debug(f'转换失败')
                        raise Exception('转换失败')
                else:
                    logger.debug(f'非linux系统, 开始使用pdf2doc进行转换')
                    convert(file_path,pdf_path)
                process_pdf(pdf_path,file_name, creat_time,end_time,file_id,folder_id=folder_id)
                update_corpus_status(ID = file_id, EMBEDDING_STATUS=1)
            else:
                # print_log(f"Unsupported file type: {file_extension}")
                logger.error(f'Unsupported file type: {file_extension}')
                update_corpus_status(ID = file_id, EMBEDDING_STATUS=-1)
        except Exception as e:
            # print_log("发生错误")
            # print('异常退出', e)
            logger.error(f'发生错误,{e}')
            traceback.print_exc()
            update_corpus_status(ID = file_id, EMBEDDING_STATUS=-1)

    return files_infos


if __name__ == '__main__':
    # main()
    # 设置任务每五分钟更新一次
    if PROD_ENV==1:
        schedule.every(5).minutes.do(main) 
        # 主循环，用于定时运行任务 
        while True: 
            schedule.run_pending() 
            time.sleep(1)
    else:
        while True:
            input(f'Press Enter to continue...')
            main()