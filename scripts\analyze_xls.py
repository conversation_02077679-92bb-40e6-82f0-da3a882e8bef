import openai

openai.api_type = "azure"
openai.api_key = "********************************"
openai.api_base = "https://af-eastus-aoai.openai.azure.com/"
openai.api_version ="2024-02-01"

def chat(question):
    response = openai.ChatCompletion.create(
        engine="4o-mini", # engine = "deployment_name".
        messages=[
            {"role": "user", "content": question}
        ],
        response_format={ "type": "json_object" }
    )

    ans=response['choices'][0]['message']['content']
    return ans


import pandas as pd
import json

def askgpt(data:dict):
    prompt='''
我有一个条目，是我们chatbot项目的单条问题测试的测试结果。内容包括：

1.用户提问的问题
2.问题反馈
3.正确答案反馈
4.存在问题
等等

现在的需求是，
1.首先判断问题是否是找不到语料，如果是找不到语料，则看看它说目标语料是什么。

请以json格式返回结果，类似这样：

{
'用户提问的问题':'xxx',
'问题':'找不到语料',
'目标语料':'xxx'
}

如果是找不到语料，但是没有提及目标语料是什么，则返回
{
'用户提问的问题':'xxx',
'问题':'找不到语料',
'目标语料':'未提及'
}

2. 如果不是找不到语料,而是其他原因比如找到语料但是没有正确答案，或者找到语料但是没有找到正确的页码，则返回
{
'用户提问的问题':'xxx',
'问题':'其他',
'目标语料':None
}

目标语料有时在 '正确答案反馈' 字段中。你要仔细辨别这个字段提及的内容是否像一个文件。有的时候文件名是一个名词性短语，也没有扩展名，比如 乳品类型以及添加物介绍 这种名词性的一个短语，也务必作为文件
'''
    answer=chat(prompt+str(data))
    print(answer)
    return answer



xls_content=pd.read_excel('./scripts/副本第一轮验收结果汇总.xlsx')

# item_template={
#     "问题":None,
#     "问题反馈":None,
#     "正确答案反馈":None,
#     "存在问题":None
# }

answer_list=[]
for i in xls_content.iloc:
    if i['场景'] == '场景1' or  i['场景'] == '场景2':
        print(f'skipped,{i["场景"]}')
        continue
    else:
        print(f'processing {i["场景"]}')
    
    item=i.copy()
    item.pop('回答')
    item.pop('来源')
    item=item.to_dict()
    # item=item_template.copy()
    # item['问题']=i['问题']
    # item['问题反馈']=i['问题反馈']
    # item['正确答案反馈']=i['正确答案反馈']
    # item['存在问题']=i['问题.1']
    answer=askgpt(item)
    answer=json.loads(answer)
    answer_list.append(answer)


with open('./scripts/answer.json','w',encoding='utf-8') as fp:
    json.dump(answer_list,fp,ensure_ascii=False,indent=4)