# Upload some documents to the index
import os
import json
import logging
logger=logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
import openai
from azure.core.credentials import AzureKeyCredential

from azure.search.documents import SearchClient
import azure.search.documents  # 导入azure模块

CONFIG_DIR = os.path.join(os.path.dirname(__file__), '../config.json')
with open(CONFIG_DIR, 'r') as f:
    config = json.load(f)
service_endpoint = config['main_rag_pipeline_config']['search_api_endpoint']
azure_key = config['main_rag_pipeline_config']['search_api_key']

credential = AzureKeyCredential(azure_key)


def fillDataToIndexFile(documents,indexName):
    print('文档内容....')
    search_client = SearchClient(endpoint=service_endpoint, index_name=indexName, credential=credential)
    result = search_client.upload_documents(documents)
    print(f"Uploaded {len(documents)} documents")

def delDataFromIndex(documents,indexName):
    print('删除索引内容....')
    search_client = SearchClient(endpoint=service_endpoint, index_name=indexName, credential=credential)

    search_client.delete_documents(documents=[documents])

    print(f"Uploaded {len(documents)} documents")
def save_or_update_content_for_index(document,indexName):
    print('更新索引内容....')
    search_client = SearchClient(endpoint=service_endpoint, index_name=indexName, credential=credential)
    print(f'索引名字 {indexName} 内容 {document}')
    search_client.merge_or_upload_documents(documents=[document])

    print("更新索引内容 SUCCESS")

def fillDataToIndex(file_id,indexname):
    with open('data/jsonvector_{}.json'.format(file_id), 'r',encoding='utf-8') as file:
        documents = json.load(file)
    search_client = SearchClient(endpoint=service_endpoint, index_name=indexname, credential=credential)
    try:
        result = search_client.upload_documents(documents)
    except azure.search.documents._search_documents_error.RequestEntityTooLargeError :
        logger.debug('单个语料大小超出限制,分批上传中')
        for i in range(0, len(documents), 100):
            sublist = documents[i:i + 100]
            result = search_client.upload_documents(sublist)

    logger.debug(f"Uploaded {len(documents)} documents")
if __name__ == '__main__':
    fillDataToIndex('vrujskjqygflufeebquqrgxziw_QA','sgsaf')