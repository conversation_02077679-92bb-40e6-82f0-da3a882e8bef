"""
修改所有的语料--
花了一个多小时，终于把字段加上了。

更新字段。 

通过函数identifiy_file_type()来识别文件类型,然后加上这个字段，然后保存到本地，人工核验一下。

ok的话再人工读入，

不行。内存会报，并且对象不一样，不能存吧。

那就一个一个来，如果有错再手动纠正。ok，就这样。
"""

import logging
logger=logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
from azure.core.credentials import AzureKeyCredential
from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential




def identify_file_type(file_name):
    support_file_type = ['pdf','xlsx','xls','png','jpg','jpeg','csv','txt','doc','docx']
    extension=file_name.split(".")[-1].lower()
    if extension not in support_file_type:
        return 'qa'
    else:
        return extension



service_endpoint = "https://afaisearch.search.windows.net"
index_name = 'sgsaf'
key = "jXlotzlexllX10tW5cfxBlazrKrprNnDKS4xAy3ypYAzSeD0lqiS"

search_client = SearchClient(service_endpoint, index_name, AzureKeyCredential(key))

# results = search_client.search(
#     search_text='*',
#     select=['id','filename','filetype'],
#     # 过滤条件是filetype不是空
#     filter='filetype eq null'
# )

# with open('./file_type_modify.log','a',encoding='utf-8') as f:
#     for result in results:
#         try:
#             filetype=identify_file_type(result['filename'])
#             print(f'{result["filename"]},{result["filetype"]} => {filetype}')
#             result['filetype'] = filetype

#             data2be_write=f'{result["id"]}/{result["filename"]}/{result["filetype"]}\n'

#             response=search_client.merge_documents(documents=[result])
#             f.write(data2be_write)
#             for i in response:print(f'modify status:',i.succeeded)
#         except Exception as e:
#             with open('./error.log','a',encoding='utf-8') as err:
#                 err.write(f'{result["id"]}/error occurred when modify the field of /{result["filename"]}/: {e}')
#             print('error occurred')



top = 1000
count_corpus = 0
with open('./file_2be_write.txt','a',encoding='utf-8') as f:
    while True:
        results = search_client.search(
            search_text='*',
            select=['id','filename','filetype'],
            # 过滤条件是filetype不是空
            filter='filetype eq null',
            top=top
        )

        results2be_write=[]
        for result in results:
            filetype=identify_file_type(result['filename'])
            str2be_write=f'{result["id"]}/{result["filename"]}/{result["filetype"]}/{filetype}\n'

            result['filetype'] = filetype
            results2be_write.append(result)

            print(str2be_write,end='')
            count_corpus+=1
            f.write(str2be_write)

        if len(results2be_write)==0:
            print('finished')
            exit(0)
        search_client.merge_documents(documents=results2be_write)

        
        print(f'已经修改了{count_corpus}条数据')