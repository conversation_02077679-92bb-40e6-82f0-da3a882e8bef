# from .retrival import Retrival
import json
import os
from typing import Optional, List, Dict, Union, Any

from azure.core.credentials import AzureKeyCredential
from azure.search.documents import SearchClient

from .retrival import BaseRetrivalModule

import logging

handler = logging.StreamHandler()
handler.setFormatter(
    logging.Formatter(
        "[%(asctime)s] [%(levelname)s] %(filename)s:%(lineno)d - %(message)s"
    )
)
handler.setLevel(logging.INFO)

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.addHandler(handler)


class AzureAISearchRetrivalModule(BaseRetrivalModule):
    def __init__(self, *, endpoint, key, index_name, **kwargs):

        self.endpoint = endpoint
        self.key = key
        self.index_name = index_name
        self.client = SearchClient(
            self.endpoint, self.index_name, credential=AzureKeyCredential(self.key)
        )

    def search(
        self,
        search_text_or_list: Union[str, List[str]],
        *,
        facets: Optional[List[str]] = None,
        filter: Optional[str] = None,
        minimum_coverage: Optional[float] = None,
        query_type: Optional[Union[str]] = None,
        search_fields: Optional[List[str]] = None,
        search_mode: Optional[Union[str]] = None,
        query_language: Optional[Union[str]] = None,
        query_caption: Optional[Union[str]] = None,
        query_caption_highlight: Optional[bool] = None,
        semantic_fields: Optional[List[str]] = None,
        semantic_configuration_name: Optional[str] = None,
        select: Optional[List[str]] = None,
        skip: Optional[int] = None,
        top: Optional[int] = None,
        vector_or_list: Optional[List] = None,
        top_k: Optional[int] = None,
        vector_fields: Optional[str] = None,
        **kwargs: Any,
    ):
        """
        从Azure AI搜索引擎中检索数据。

        此函数是对azure.search.documents.SearchClient的search方法的封装,添加了对多个查询的并行搜索支持,返回所有查询的结果。

        Args:
            search_text_or_list: Union[str,List[str]]: 要搜索的文本或者列表。如果为字符串，则会被当作单个文本进行搜索；如果是列表，则会分别对每个元素进行搜索。
            vector_or_list: Optional[List]: Optional[List]. 向量或者向量列表。如果为向量，则会被当作单个向量进行搜索；如果是向量列表，则会分别对每个向量进行搜索。如果想要使用混合搜索，则需要同时传入search_text_or_list和vector_or_list，且二者具有同样的长度。
            other_args: please refer to the official documentation of azure.search.documents.SearchClient.search


        Returns:
            result_list: List[Dict]

                It's as follows:

                [
                    {
                        "search_text": "search_text",
                        "hybrid_search_results": [
                            {
                                "id": "id",
                                "score": "score"
                            }
                        ]
                    },
                    ...
                ]
        """
        if isinstance(search_text_or_list, str):
            search_text_or_list = [search_text_or_list]

        hybrid_search = False
        vector_search = False
        keyword_search = False

        if (
            search_text_or_list == [""]
            or search_text_or_list == []
            or search_text_or_list == [None]
        ):
            if not vector_or_list:
                raise ValueError("没有输入向量或搜索文本")
            else:
                vector_search = True
                logger.info("开始向量检索")
                if isinstance(vector_or_list[0], float):
                    vector_or_list = [vector_or_list]
        else:
            if not vector_or_list:
                keyword_search = True
                logger.info("开始关键词检索")

                if isinstance(search_text_or_list, str):
                    search_text_or_list = [search_text_or_list]
            elif len(vector_or_list) == len(search_text_or_list):
                hybrid_search = True
                logger.info("开始混合检索")
            else:
                raise ValueError(
                    "同时传入了文本和向量，但是向量数量和搜索文本数量不匹配"
                )

        result_list = []

        # 2. 定义不同检索操作
        if hybrid_search:
            for vector, search_text in zip(vector_or_list, search_text_or_list):
                results = self.client.search(
                    search_text=search_text,
                    facets=facets,
                    filter=filter,
                    minimum_coverage=minimum_coverage,
                    query_type=query_type,
                    search_fields=search_fields,
                    search_mode=search_mode,
                    query_language=query_language,
                    query_caption=query_caption,
                    query_caption_highlight=query_caption_highlight,
                    semantic_fields=semantic_fields,
                    semantic_configuration_name=semantic_configuration_name,
                    select=select,
                    skip=skip,
                    top=top,
                    vector=vector,
                    top_k=top_k,
                    vector_fields=vector_fields,
                    **kwargs,
                )

                # 3. 真正的获得检索结果在这里
                result_list.append(
                    {
                        "search_text": search_text,
                        "hybrid_search_results": [i for i in results],
                    }
                )
        elif vector_search:
            for vector in vector_or_list:
                results = self.client.search(
                    search_text="",
                    facets=facets,
                    filter=filter,
                    minimum_coverage=minimum_coverage,
                    query_type=query_type,
                    search_fields=search_fields,
                    search_mode=search_mode,
                    query_language=query_language,
                    query_caption=query_caption,
                    query_caption_highlight=query_caption_highlight,
                    semantic_fields=semantic_fields,
                    semantic_configuration_name=semantic_configuration_name,
                    select=select,
                    skip=skip,
                    top=top,
                    vector=vector,
                    top_k=top_k,
                    vector_fields=vector_fields,
                    **kwargs,
                )
                result_list.append({"vector_search_results": [i for i in results]})
        else:
            for search_text in search_text_or_list:
                results = self.client.search(
                    search_text=search_text,
                    facets=facets,
                    filter=filter,
                    minimum_coverage=minimum_coverage,
                    query_type=query_type,
                    search_fields=search_fields,
                    search_mode=search_mode,
                    query_language=query_language,
                    query_caption=query_caption,
                    query_caption_highlight=query_caption_highlight,
                    semantic_fields=semantic_fields,
                    semantic_configuration_name=semantic_configuration_name,
                    select=select,
                    skip=skip,
                    top=top,
                    **kwargs,
                )

                result_list.append(
                    {
                        "search_text": search_text,
                        "keywords_search_results": [i for i in results],
                    }
                )

        return result_list

    def filter_list2filter(self, filters: Dict[str, Union[List, None]]) -> str:
        """Convert a list of filters to a string that can be used as the `filter` parameter in an Azure AI Search API call.

        eg:
        filters:
        {
            "name": ["John", "Jane"],
            "age": [30,40],
            "gender": ["male","female"]
        }

        to:
        "name eq 'John' or name eq 'Jane' and age ge 30 and age le 40 and gender eq 'male' or gender eq 'female'"

        列表可空，可None，可不传
        """
        filter_string = ""
        first_flg = 0
        for field, values in filters.items():
            temp_filter = ""
            if len(values) == 0 or values == None:
                continue

            if field == "endtime":
                temp_filter = values
            else:
                if type(values[0]) == str:
                    temp_filter = " or ".join(
                        [f"{field} eq '{value}'" for value in values]
                    )
                else:
                    temp_filter = " or ".join(
                        [f"{field} eq {value}" for value in values]
                    )
            if temp_filter:
                if not first_flg:
                    filter_string += f"( {temp_filter} )"
                    first_flg = 1
                else:
                    filter_string += f" and ( {temp_filter} )"

        return filter_string if filter_string else ""

    def delete(
        self,
        filter_string: str = "",
        skip_search: bool = False,
        docs_to_delete: List[Dict] = None,
    ):
        """Delete documents from the Azure AI Search index.

        Args:
            filter (Dict[str,List]): A dictionary of filters to use to identify the documents to delete.

        Returns:
            result (Dict): A dictionary containing the result of the delete operation.
        """
        if skip_search:
            return self.client.delete_documents(documents=docs_to_delete)

        # if not filter_string:
        #     logger.error(f'you are executing delete operation, but filter is empty! ')
        #     raise Exception('you are executing delete operation, but filter is empty!')

        logger.warning(f"your filter is: {filter_string}")
        ans = input("do you want to continue? (y/n)")
        if ans != "y":
            logger.warning(f"you have canceled the delete operation!")
            return None

        search_result = self.search(
            search_text_or_list="*",
            filter=filter_string,
            select=["id", "idfile", "content", "folder_id", "filename"],
        )

        doc_key_list = []
        count = 0
        for search_result_item in search_result[0]["keywords_search_results"]:
            doc_key_list.append({"id": search_result_item["id"]})
            count += 1
            logger.info(f"found {count} documents.")

        num_doc = len(doc_key_list)
        if num_doc == 0:
            logger.warning(f"no documents found to delete!")
            return None
        else:
            logger.warning(
                f"found {len(doc_key_list)} documents to delete, this operation is irreversible!"
            )
        ans = input("do you want to continue? (y/n)")
        with open("delete_doc_key_list_temp.json", "w", encoding="utf-8") as fp:
            json.dump(doc_key_list, fp, ensure_ascii=False, indent=4)
        if ans != "y":
            logger.warning(f"you have canceled the delete operation!")
            return None
        result = self.client.delete_documents(documents=doc_key_list)

        # 删除临时文件
        os.remove("delete_doc_key_list_temp.json")

        return result
