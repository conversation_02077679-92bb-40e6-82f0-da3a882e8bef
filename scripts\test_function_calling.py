'''
gpt function calling测试脚本
'''

import openai
import json

openai.api_type = "azure"
openai.api_key = "********************************"
openai.api_base = "https://af-eastus-aoai.openai.azure.com/"
openai.api_version ="2024-02-01"


tools = [
    {
        "type": "function",
        "function": {
            "name": "askweather",
            "description": "Ask about weather",
            "parameters": {
                "type": "object",
                "properties": {
                    "time": {
                        "type": "string",
                        "description": '请求的天气的时间，格式为YYYY-MM-DD HH:mm，如2019-08-05 14:30',
                    },
                    "location": {
                        "type": "string",
                        "description": '请求的天气所在城市，如 Beijing. 你需要先获得城市的名称 ',
                    }
                },
                "required": ["time", "location"],
            },
        }
    },
    {
    "type": "function",
    "function": {
        "name": "getlocation",
        "description": "get current location, return city name",
        "parameters": {
            "type": "object",
            "properties": {
            },
            "required": [],
        },
    }
}
]

def getlocation():
    print('调用函数')
    return 'Beijing'


messages=[
    {
        "role": "user",
        "content": "I want to know the weather here at 2019-08-05 14:30.",
    }
]

def askweather(time, location):
    print('调用函数')
    return '天气晴'

while(True):
    response=openai.ChatCompletion.create(
        engine="AF-gpt4o",
        messages=messages,
        tools=tools,
    )
    messages.append(response.choices[0].message.to_dict_recursive())


    if 'tool_calls' in response['choices'][0]['message']:
        print('调用函数')
        function_name=response['choices'][0]['message']['tool_calls'][0]['function']['name']
        function_parameters=json.loads(response['choices'][0]['message']['tool_calls'][0]['function']['arguments'])
        fucntion_id=response['choices'][0]['message']['tool_calls'][0]['id']
        function_calling_params=f'''
        function_name        :{function_name}
        function_parameters  :{function_parameters}
        function_id          :{fucntion_id}'''
        print(function_calling_params)
        input()
        if function_name=='askweather':
            result=askweather(function_parameters['time'], function_parameters['location'])
        if function_name=='getlocation':
            result=getlocation()
            
        messages.append({
            "role":"tool",
            "tool_call_id":fucntion_id,
            "name":function_name,
            "content":result
        })

    else :
        print('没有调用函数,退出')
        print(response['choices'][0]['message']['content'])
        break

