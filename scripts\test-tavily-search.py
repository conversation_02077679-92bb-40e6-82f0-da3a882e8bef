 
from tavily import TavilyClient
import json
tavily = TavilyClient(api_key='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')
 
# 基本搜索：
response = tavily.search(query="Should I invest in Apple in 2024?")
# print('For basic search: \n',response)
# print(response['results'])
# [print(i) for i in response['results']]
print(json.dumps(response, indent=4))

input('input 回车键继续')
 
# 高级搜索：
response = tavily.search(query="Should I invest in Apple in 2024?", search_depth="advanced")
print('For advanced search: \n',response)
print(json.dumps(response, indent=4))
input('input 回车键继续')
 
# 获取搜索结果作为传递给LLM的上下文：
# context = [{"url": obj["url"], "content": obj["content"]} for obj in response.results]
 
# 您可以轻松地根据任何最大标记获取搜索结果上下文，直接传递给您的RAG。
# 响应是在最大标记限制内的上下文字符串。
response = tavily.get_search_context(query="What happened in the burning man floods?", search_depth="advanced", max_tokens=1500)
print('get_search_context: \n',response)
input('input 回车键继续')
 
# 您还可以通过一个简单的函数调用，获得包含相关来源的简单问题答案：
response = tavily.qna_search(query="Where does Messi play right now?")
print('qna_search: \n',response)
input('input 回车键继续')
 
 
response = tavily.qna_search(query="一个处女座的程序猿是来自哪个社区的博主？请你简要说一下这位博主的成就")
print('qna_search: \n',response)
 