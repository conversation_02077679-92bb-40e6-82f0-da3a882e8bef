import sys
import os
import json

# 获取当前脚本的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取 chatcode 模块的绝对路径
module_path = os.path.abspath(os.path.join(current_dir, "../../"))
sys.path.append(module_path)

from chatcode.askdoc.retrival.azure_aisearch_retrival import AzureAISearchRetrivalModule

config_path = os.path.join(
    os.path.dirname(__file__), "../../chatcode/askdoc/config.json"
)
with open(config_path, "r", encoding="utf-8") as f:
    config = json.load(f)
config_search = {
    "endpoint": config["main_rag_pipeline_config"]["search_api_endpoint"],
    "key": config["main_rag_pipeline_config"]["search_api_key"],
    "index_name": config["main_rag_pipeline_config"]["search_api_index_name"],
}
print(f"using config:\n{config_search}")

if __name__ == "__main__":
    retrival_module = AzureAISearchRetrivalModule(**config_search)

    # filter = {'folder_id':[]}
    filter = {"idfile": ['13801']}
    filter_string = retrival_module.filter_list2filter(filter)
    print(filter_string)
    result = retrival_module.delete(filter_string=filter_string)
