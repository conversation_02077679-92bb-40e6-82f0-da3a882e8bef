from fastapi import FastAPI
import os
import time
import threading
import asyncio
asyncio.get_event_loop().set_debug(True)

import uvicorn
import time
from openai import AsyncAzureOpenAI,AsyncOpenAI,AzureOpenAI,OpenAI
from anyio.lowlevel import RunVar
from anyio import CapacityLimiter

app = FastAPI()
# @app.on_event("startup")
# def startup():
#     print("start")
#     RunVar("_default_thread_limiter").set(CapacityLimiter(200))


client = AsyncAzureOpenAI(
  azure_endpoint = 'https://af-koreacentral-aoai.openai.azure.com/',
  api_key='B1EksdBCKXSWa8WhfWmNzJth8An4R9IH9XTbnwki161GrmZHfWioJQQJ99AKACNns7RXJ3w3AAABACOGGwBu',  
  api_version="2024-02-01"
)

# client = AsyncOpenAI(
#       api_key = '8f719490-1eaf-4a9c-be9b-804638cae3cc',
#       base_url = 'https://ark.cn-beijing.volces.com/api/v3'
# )

# client = OpenAI(
#       api_key = '8f719490-1eaf-4a9c-be9b-804638cae3cc',
#       base_url = 'https://ark.cn-beijing.volces.com/api/v3'
# )

# client = AzureOpenAI(
#   azure_endpoint = 'https://af-koreacentral-aoai.openai.azure.com/',
#   api_key='B1EksdBCKXSWa8WhfWmNzJth8An4R9IH9XTbnwki161GrmZHfWioJQQJ99AKACNns7RXJ3w3AAABACOGGwBu',  
#   api_version="2024-02-01"
# )


# thead_count = 0
# # 同步处理
# @app.post("/chatHTTP")
# def chat(data:dict):
#     global thead_count
#     pid = os.getpid()
#     thread_id = threading.get_ident()
#     thead_count += 1
#     print(f'thsis is pid {pid}s thread {thread_id},we have opened {thead_count} threads')
#     # azureopenai client
#     count = 0 

#     time1=time.time()
#     response = client.chat.completions.create(
#         model="AF-gpt4o", # model = "deployment_name"
#         messages=[
#             {"role": "user", "content": "hi"}
#         ],
#     )
#     time2=time.time()
#     print(f'cost {time2-time1} s')

#     # openai client
#     # response = client.chat.completions.create(
#     # model="ep-20241202112808-kxxl7", # model = "deployment_name"
#     #     messages=[
#     #         {"role": "user", "content": "who are you"}
#     #     ],
#     # )

#     # time.sleep(2)
#     res = {'answer':response}
#     res = 1
#     return res


@app.post("/chatHTTP")
async def chat(data:dict):
    # # async openai client
    # response = await client.chat.completions.create(
    # model="ep-20241202112808-kxxl7", # model = "deployment_name"
    #     messages=[
    #         {"role": "user", "content": "who are you"}
    #     ],
    # )

    response = await client.chat.completions.create(
    model="AF-gpt4o", # model = "deployment_name"
        messages=[
            {"role": "user", "content": "hi"}
        ],
    )

    res = {'answer':response}
    # await asyncio.sleep(2)
    # res = 1
    return res


if __name__ == "__main__":
    uvicorn.run('new_version:app', host="0.0.0.0", port=8000,workers=16,limit_concurrency=2000,limit_max_requests=  2000,http='h11')