import sys
sys.path.append('./chatcode')

from askdoc.pipelines.search_corpus_pipeline import MainRAGPipeline

import json

def main():
    main_rag_pipeline = MainRAGPipeline(
        azure_openai_api_key='********************************',
        azure_openai_api_base='https://af-eastus-aoai.openai.azure.com/',
        azure_openai_api_version='2024-02-01',
        azure_openai_api_engine='AF-gpt4o',
        search_api_key='jXlotzlexllX10tW5cfxBlazrKrprNnDKS4xAy3ypYAzSeD0lqiS',
        search_api_index_name='sgsaf',
        search_api_endpoint='https://afaisearch.search.windows.net',
    )

    result = main_rag_pipeline.run(
        query="皮革水解物的危害是什么",
        dialog_history=[],
        search_filter={}
    )

    return result

if __name__ == '__main__':
    print(f'{json.dumps(main(), indent=4, ensure_ascii=False)}')
