'''
测试azure搜索功能脚本。运行后返回向量搜索结果
'''

from azure.core.credentials import AzureKeyCredential
from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential
import tiktoken
import sys
sys.path.append('/home/<USER>/jack/chatbot-alg/chatcode')
sys.path.append('/home/<USER>/jack/chatbot-alg/chatcode/askdoc')
from askdoc.utils.vectorCreate import generate_embeddings
import datetime
import logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(filename)s - %(lineno)d - %(message)s'
)
logger=logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


service_endpoint ="https://afaisearch.search.windows.net"  #"https://sgsafaisearch.search.windows.net"
azure_key ="****************************************************"  # "y69GPCEixQ0r68gckvRHufxlxUf6vv6TCgyRuAtxWxAzSeD6UyKe"
credential = AzureKeyCredential(azure_key)

def num_tokens_from_messages(message):
    num_tokens=0
    encoding = tiktoken.get_encoding("cl100k_base")
    for key, value in message.items():
        if key == "content":  # if there's a name, the role is omitted
            num_tokens += len(encoding.encode(value))  # role is always required and always 1 token
    #print(num_tokens)
    return num_tokens



def searchIndex(query,index_name,idlist=[],folder_id_list=[],filename_list=[],core='gpt-4-1106-preview'):
    '''
    通过传入的query搜索，并使用idlist，folder_id_list,filename_list三个过滤条件来过滤。如果不传入某个参数或传入空列表，则不使用该过滤条件。
    如：

    query='白酒',
    index_name='sgsaf',
    idlist=[],
    folder_id_list=['测试语料','测试语料2','test1'],
    filename_list=[]

    对应的最终查询语句为：

    (folder_id eq '测试语料' or folder_id eq '测试语料2' or folder_id eq 'test1') and ((endtime gt '2024-08-02 14:52:15') or (endtime eq ''))

    即idlist和filename_list没有加入到过滤条件中,只使用了folder_id_list和expiretime作为过滤条件。过期时间的过滤条件是默认的。
    '''
    print(f'--------------------------正在{filename_list}中搜索',query)
    search_client = SearchClient(service_endpoint, index_name, credential=credential)
    current_time=datetime.datetime.now()


    filter_fileid=' or '.join([f"idfile eq '{i}'" for i in idlist])
    filter_folderid=' or '.join([f"folder_id eq '{i}'" for i in folder_id_list])
    filter_filename=' or '.join([f"filename eq '{i}'" for i in filename_list])
    current_time=datetime.datetime.now()
    filter_expiretime=f"(endtime gt '{current_time.strftime('%Y-%m-%d %H:%M:%S')}') or (endtime eq '')"
    logger.debug(f'your fileid filter is {filter_fileid}')
    logger.debug(f'your folder fileter is {filter_folderid}')
    logger.debug(f'your filenmae fileter is {filter_filename}')
    logger.debug(f'your expiretime filter is {filter_expiretime}')

    final_filter_list=[]
    final_filter_list.append(filter_fileid)
    final_filter_list.append(filter_folderid)
    final_filter_list.append(filter_filename)
    final_filter_list.append(filter_expiretime)

    first_flag=True
    final_filter=''
    for i in final_filter_list:
        if i == '':
            continue
        else:
            if first_flag:
                first_flag=False
                final_filter+=f'({i})'
            else :
                final_filter+=f' and ({i})'
    logger.debug(f'your final filter is {final_filter}')
    # BATCH_SIZE = 500
    
    # # 计算需要的批次数量
    # num_batches = (len(idlist) + BATCH_SIZE - 1) // BATCH_SIZE
    vector,embeddings_model,embeddings_tokens=generate_embeddings(query)
    logger.debug(f'your final filter is {final_filter}')
        

    results = search_client.search(
        search_text=query,
        # update to vector_query fileds 放在query里了，
        vector=vector,
        #vector_queries = [vector_query],
        top_k=2,
        vector_fields="contentVector",
        # query_type="semantic", query_language="zh-cn", semantic_configuration_name='my-semantic-config',
        # query_caption="extractive", query_answer="extractive",
        select=["content","tag",'idfile'],
        filter=final_filter,
    )

    i=0
    contents=[]
    num_tokens=0
    max_token=12000
    if(core=='gpt16'):
        max_token=12000

    sum=0
    # logger.debug(f'results:{results}')

    for result in results:
        sum=sum+1
        if(sum>3):
            break
        tknum=num_tokens_from_messages(result)
        print('token 数：',tknum)
        i+=1
        num_tokens=num_tokens+tknum
        if num_tokens > max_token:
            break
        print(f"Score: {result['@search.score']}")
        print(f"Content: {result['content']}")
        contents.append(result)
    logger.debug(f'搜索到内容如下：{contents}')
    print('-----------------------------------------本次搜索结束--------------------------------------------\n')
    return contents,embeddings_model,embeddings_tokens


searchIndex(
    query='白酒',
    index_name='sgsaf',
    idlist=[],
    folder_id_list=['测试语料','测试语料2','test1'],
    filename_list=[]
)