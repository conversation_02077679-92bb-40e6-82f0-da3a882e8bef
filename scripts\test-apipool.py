# from openai import AzureOpenAI
import openai
# client = AzureOpenAI(
#     azure_endpoint="app-52ezhysttga2w.jollypebble-72e46cd0.southeastasia.azurecontainerapps.io",  #if you deployed to Azure Container Apps, it will be 'https://app-[something].[region].azurecontainerapps.io'
#     api_key="does-not-matter", #The api-key sent by the client SDKs will be overriden by the ones configured in the backend environment variables
#     api_version="2024-02-01"
# )

openai.api_key= '' 
openai.api_base = "https://app-52ezhysttga2w.jollypebble-72e46cd0.southeastasia.azurecontainerapps.io"
openai.api_version ="2024-02-01"
openai.api_type = "azure"

# response = client.chat.completions.create(
#     model="AF-gpt4o",
#     messages=[
#         {"role": "system", "content": "You are a helpful assistant."},
#         {"role": "user", "content": "What is the first letter of the alphabet?"}
#     ]
# )

response=openai.ChatCompletion.create(
    engine='AF-gpt4o', # The deployment name you chose when you deployed the GPT-35-turbo or GPT-4 model.
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What is the first letter of the alphabet?"}
    ]
)
print(response)