import json
from typing import Any
from .filter import BaseFilterModule

import openai
from typing import Optional, Union, List, Dict, Tuple
import textwrap


class AzureOpenAIFilterModule(BaseFilterModule):
    def __init__(
        self,
        api_key: str,
        api_base: str,
        api_version: str,
    ):

        self.api_type = "azure"
        self.api_key = api_key
        self.api_base = api_base
        self.api_version = api_version

    def filter(
        self,
        query: str,
        search_result_list: List,
    ) -> Tuple[List[Dict], str, int]:
        """
        Filter documents based on the query and search result list.

        Args:
            query (str): The user query which is based to filter search_result_list.
            search_result_list (List): The list of documents which are searched by the user query. Elements in list is not confined, it will be  transformed to str.

        Returns:
            result (List): The list of documents which are selected by the user query. The format is as follows:

                [{"filename":"file1","idfile":"1"},{"filename":"file2","idfile":"2"}]

            model (str): The model used to generate the response.
            usage (int): The number of tokens used to generate the response.

        """
        openai.api_type = self.api_type
        openai.api_key = self.api_key
        openai.api_base = self.api_base
        openai.api_version = self.api_version

        prompt = textwrap.dedent(
            """
        请你扮演一个文档筛选机器人。用户会给你一个问题以及搜索到的多个文档，请判断哪些文档可能包含问题的答案，并筛选出这些相关文档。你的输出应为 JSON 格式。格式如下:{"selected_document": [fileid1, fileid2,]}

        示例：
        问题：乳粉的感官要求是什么？
        待筛选文档：[{"filename":"乳粉国家标准","idfile":"12258","content":document_content},{"filename":"乳粉中污染物含量要求.pdf","idfile":"27736","content":document_content}]
        输出：{"selected_documents":[{"filename":"乳粉国家标准","idfile":"12258"}]}

        文档数量没有限制。
        """
            + f"请求：{query} 搜索到的文档的名字： {search_result_list}"
        )

        messages = [
            {"role": "system", "content": prompt},
        ]

        json_schema = {
            "name": "selected_documents",  
            "schema": {
                "type": "object",
                "properties": {
                    "selected_documents": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "filename": {
                                    "type": "string",
                                    "description": "The name of the document.",
                                },
                                "idfile": {
                                    "type": "string",
                                    "description": "The unique identifier of the document.",
                                },
                            },
                            "required": ["filename", "idfile"],
                            "additionalProperties": False,  
                        },
                    },
                },
                "required": ["selected_documents"],
                "additionalProperties": False,  
                "description": "Schema for selected documents.",  
            },
            "strict": True,
        }
        response_format = {"type": "json_schema", "json_schema": json_schema}

        response = openai.ChatCompletion.create(
            engine="4o-mini",
            messages=messages,
            response_format=response_format,
        )

        result = json.loads(response.choices[0].message.content)["selected_documents"]
        model = response.model
        usage = response.usage.total_tokens
        return result, model, usage
