import pandas as pd
import openpyxl

def excel_to_markdown(file_path, sheet_name=None):
    """
    将 Excel 表格中的内容转换为 Markdown 表格格式。
    
    :param file_path: Excel 文件路径
    :param sheet_name: 指定的 sheet 名称或索引，默认为 None 表示处理所有 sheet
    :return: 返回一个字典，其中键是 sheet 名称，值是对应的 Markdown 表格字符串
    """
    # 使用 openpyxl 加载工作簿
    workbook = openpyxl.load_workbook(file_path, data_only=True)
    sheets = workbook.sheetnames if sheet_name is None else [sheet_name]

    all_markdown_tables = {}

    for sheet_name in sheets:
        sheet = workbook[sheet_name]

        # 获取表格的行数和列数，创建一个填充了 None 的二维数组
        max_rows = sheet.max_row
        max_cols = sheet.max_column
        data = [[None] * max_cols for _ in range(max_rows)]  # 初始化为 None 的二维数组

        # 将表格内容填充到 data 中
        for i, row in enumerate(sheet.iter_rows(values_only=True)):
            for j, cell_value in enumerate(row):
                data[i][j] = cell_value
        print(f"数据填充完成，前几行内容: {data[:3]}")

        # 展开合并单元格内容
        for merged_cell in sheet.merged_cells:
            min_row, min_col, max_row, max_col = merged_cell.bounds
            cell_value = sheet.cell(row=min_row, column=min_col).value

            # 调整索引为 0 基准
            min_row, min_col = min_row - 1, min_col - 1
            max_row, max_col = max_row - 1, max_col - 1

            # 确保索引不超出范围
            if max_row >= max_rows or max_col >= max_cols:
                print(f"合并单元格 ({min_row + 1}, {min_col + 1}) 到 ({max_row + 1}, {max_col + 1}) 超出范围，已跳过")
                continue

            # 填充合并区域内的每个单元格
            print(f"处理合并单元格: ({min_row+1}, {min_col+1}) 到 ({max_row+1}, {max_col+1})，值: {cell_value}")
            for row in range(min_row, max_row + 1):
                print(f"第 {i+1} 行数据: {row}")
                for col in range(min_col, max_col + 1):
                    data[row][col] = cell_value

        # 转换为 DataFrame 以便生成 Markdown
        df = pd.DataFrame(data)
        df = df.dropna(axis=1, how='all')  # 去掉全空的列
        df = df.dropna(axis=0, how='all')  # 去掉全空的行
        markdown_table = df.to_markdown(index=False, headers="keys", tablefmt="pipe")
        all_markdown_tables[sheet_name] = markdown_table

    return all_markdown_tables


# 将内容分割为指定最大token数量的chunk
def split_into_chunks(content, max_token=1024):
    tokens = content.split()  # 将内容分割为单词（或标记）
    chunks = []
    chunk = []
    current_token_count = 0
    
    for token in tokens:
        current_token_count += len(token)
        if current_token_count > max_token:
            chunks.append(' '.join(chunk))
            chunk = [token]
            current_token_count = len(token)
        else:
            chunk.append(token)
    
    if chunk:
        chunks.append(' '.join(chunk))
    
    return chunks, len(chunks)

