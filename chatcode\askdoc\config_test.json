{"main_rag_pipeline_config": {"azure_openai_api_key": "********************************", "azure_openai_api_base": "https://wysengine.openai.azure.com/", "azure_openai_api_version": "2024-12-01-preview", "azure_openai_api_engine": "4o", "search_api_key": "jXlotzlexllX10tW5cfxBlazrKrprNnDKS4xAy3ypYAzSeD0lqiS", "search_api_index_name": "sgsaf", "search_api_endpoint": "https://afaisearch.search.windows.net", "websearch_api_key": "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", "tools": [{"type": "function", "function": {"name": "search_all_corpus", "description": "这是公司内部存放标准文件的数据库的一个接口函数。通过传入用户的query，可以直接搜索数据库中文件的相关内容。比如用户询问乳粉的霉菌标准是多少？你应当生成合适的query。函数会找到最相关的几个文本段落返回,并附带相关数量、来源、页数等信息。如果某次搜索结果不理想，可以尝试调整query。对于任何可能相关的问题，你应当**优先使用这个函数**.", "parameters": {"type": "object", "properties": {"rewritten_keywords": {"type": "string", "description": "你传入的关键字，通常为几个总结的关键字。虽然该字段不再是搜索的关键部分，但还是要输出它。**当用户提问涉及国际标准，你应当重写为双语格式的query**"}, "rewritten_query_list": {"type": "array", "items": {"type": "string"}, "description": "LLM重写的query列表，当前列表只需包含**一个**重写后的query。**当用户提问涉及国际标准，尤其是欧盟标准，你应当重写为双语格式的query**"}}, "required": ["rewritten_keywords", "rewritten_query_list"]}}}], "prompt": [{"role": "system", "content": "请你扮演sgs公司的文档管理员，用户可能会向你提问问题，只要你不知道/不确定，就去数据库中搜索。请以json格式回复，回复内容包括你的回答和引用的语料。json格式结构如下：\n            {\"answer\":\"xxxx\",\n                \"references\":[\n                    {\"idfile\":\"xx\",\n                    \"filename\":\"文件的名字\",\n                    \"tag\":\"\",},\n                    ...]\n            其中，idfile，filename，tag请使用搜索到的语料的idfile，filename，tag。filename通常是文件的名字，tag通常是： '来源：xxxxx 第几页' 这种格式\n\n            1. 如果你的回答过程不涉及语料，则references字段中的内容可以设为null。"}]}, "other_config": {"embedding_config": {"azure_openai_api_key": "********************************", "azure_openai_api_base": "https://af-eastus-aoai.openai.azure.com/", "azure_openai_api_version": "2024-02-01"}}, "getfiles_config": {"params": {"FILE_ID": "3390", "token": "b6bf96c82e81bfd411610a021de23a30", "ORG_ID": "17"}, "local_filename": "downloaded_file.docx", "loginurl": "https://ticbot.sgsonline.com.cn/backend/login", "loginparams": {"USER_NAME": "johndoe", "PASSWORD": "Hfc@2020"}, "orgsurl": "https://ticbot.sgsonline.com.cn/backend/org/orgsListPage", "orgsparams": {"token": "johndoe"}, "filename_url": "https://ticbot.sgsonline.com.cn/backend/chat-corpus/query-corpus-meta-gpt", "filename_params": {"TYPE": "2", "token": "TEST", "ORG_ID": "17", "totalPage": "1", "showCount": "100000"}, "xml_content": "你的XML内容", "download_url": "https://ticbot.sgsonline.com.cn/backend/chat-corpus/downloadFile", "download_params": {"token": "TEST", "ORG_ID": "17"}, "download_directory": "downloads"}, "requestsfile_config": {"params": {"FILE_ID": "3390", "token": "b6bf96c82e81bfd411610a021de23a30", "ORG_ID": "13"}, "local_filename": "downloaded_file.docx", "loginurl": "https://ticbot.sgsonline.com.cn/backend/login", "loginparams": {"USER_NAME": "johndoe", "PASSWORD": "Hfc@2020"}, "orgsurl": "https://ticbot.sgsonline.com.cn/backend/org/orgsListPage", "orgsparams": {"token": "johndoe"}, "filename_url": "https://ticbot.sgsonline.com.cn/backend/chat-corpus/query-corpus-meta-gpt", "filename_params": {"TYPE": "1", "token": "TEST", "ORG_ID": "1", "totalPage": "1", "showCount": "100000"}, "xml_content": "你的XML内容", "download_url": "https://ticbot.sgsonline.com.cn/backend/chat-corpus/downloadFile", "download_params": {"token": "TEST", "ORG_ID": "1"}, "download_directory": "downloads"}, "getqa_config": {"params": {"FILE_ID": "3390", "token": "b6bf96c82e81bfd411610a021de23a30", "ORG_ID": "13"}, "local_filename": "downloaded_file.docx", "loginurl": "https://ticbot.sgsonline.com.cn/backend/login", "loginparams": {"USER_NAME": "johndoe", "PASSWORD": "Hfc@2020"}, "orgsurl": "https://ticbot.sgsonline.com.cn/backend/org/orgsListPage", "orgsparams": {"token": "johndoe"}, "filename_url": "https://ticbot.sgsonline.com.cn/backend/chat-corpus/query-corpus-meta-gpt", "filename_params": {"TYPE": "0", "token": "TEST", "ORG_ID": "1", "totalPage": "1", "showCount": "100000"}, "xml_content": "你的XML内容", "download_url": "https://ticbot.sgsonline.com.cn/backend/chat-corpus/downloadFile", "download_params": {"token": "TEST", "ORG_ID": "1"}, "download_directory": "downloads"}, "database_config": {"host": "sgsdbcne2flexiblemysqlafkmsprod.mysql.database.chinacloudapi.cn", "user": "sgs_kms_user", "pwd": "SH128_pkj45_J25gY_Kj12d", "db": "<PERSON><PERSON><PERSON><PERSON>"}}