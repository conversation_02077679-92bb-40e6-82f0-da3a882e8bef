import os
import threading
import logging
import textwrap
import json
from json import JSONDecodeError
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Union,Optional,List,Dict,Tuple

from .pipeline import BasePipline
from ..generation.azure_openai_generation import AzureOpenAIGenerationModule
from ..embedding.azure_openai_embedding import AzureOpenAIEmbeddingModule
from ..retrival.azure_aisearch_retrival import AzureAISearchRetrivalModule
from ..filter.azure_openai_filter import AzureOpenAIFilterModule
from ..websearch.tavily_websearch import TavilyWebSearchModule

handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('[%(asctime)s] [%(levelname)s] %(filename)s:%(lineno)d - %(message)s'))
handler.setLevel(logging.DEBUG)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
logger.addHandler(handler)


module_dir = os.path.dirname(os.path.abspath(__file__))
config_file_path = os.path.join(module_dir,'../','config.json')
with open(config_file_path, 'r', encoding='utf-8') as f:
    config = json.load(f)

class PipelineContext:
    """
    包含了各个pipeline之间需要交换的数据, 作为参数传递到用到的pipeline中。这样万一以后传递来的参数发生变化的时候不用再逐个修改不同pipeline的run的函数参数"""
    def __init__(self):
        self.total_usage = {
            'chat_usage':{},
            'embedding_usage':{}
        }


class SearchAllCorpusPipeline(BasePipline):
    """Search all corpuses
    1. 变量定义：
        - total_usage: 总用量
            format: {
                'chat_usage':{
                    'model1':0,
                    'model2':0
                    }
                'embedding_usage':{
                    'model1':0,
                    'model2':0
                    }
            }
        
        - rewrite_query_list: 重写的query list
        - rewrite_query_vector_list: 重写的query向量list
        - stage1_search_results: 第一阶段搜索结果
        - stage2_filtered_search_results: 第二阶段过滤后的搜索结果
        - stage3_final_search_results: 最终搜索结果
        
        input:
        
        rewrite_query_list: List[str] 重写的query list
        rewrite_keywords_list: List[str] 重写的关键词list
        search_filter: Dict 过滤器
            eg {
                'fileid':[],
                'folder_ids':[],
            }
        
        output:

        final_search_results: List[dict] 最终搜索结果
        total_usage: Dict 总用量
        """
    def __init__(
            self,
            azure_openai_api_key: str,
            azure_openai_api_base: str,
            azure_openai_api_version: str,
            search_api_endpoint: str,
            search_api_key: str,
            search_api_index_name: str
    ):  
        logger.info('初始化 MainRAGPipeline')
        self.api_key = azure_openai_api_key
        self.api_base = azure_openai_api_base
        self.api_version = azure_openai_api_version

        self.search_api_endpoint = search_api_endpoint
        self.search_api_key = search_api_key
        self.search_api_index_name = search_api_index_name

        '''
        self.total_usage is like:

        self.total_usage = {
            'chat_usage':{
                'gpt-4o-mini':xxxx,
                'gpt-4o':xxxx,
                'gpt-3.5-turbo':xxxx,
                .....
                },
            'embedding_usage':{
                'text-embedding-large-003':xxx,
                .....
                }
        }
        '''

        embedding_config = config.get('other_config',{}).get('embedding_config',{})
        if embedding_config:
            logger.debug(f'using special embedding config')
            self.embedding_module = AzureOpenAIEmbeddingModule(
                api_base=embedding_config.get('azure_openai_api_base'),
                api_key=embedding_config.get('azure_openai_api_key'),
                api_version=embedding_config.get('azure_openai_api_version'),
            )
        else:
            logger.error(f'未找到embedding配置')
            exit(1)

        
        self.retrival_module = AzureAISearchRetrivalModule(
            endpoint=self.search_api_endpoint,
            key=self.search_api_key,
            index_name=self.search_api_index_name
        )
        self.filter_module = AzureOpenAIFilterModule(
            api_key=self.api_key,
            api_base=self.api_base,
            api_version=self.api_version
        )

    def run(
            self,
            pipeline_context: PipelineContext,
    ):
        """run the pipeline"""

        """1. 准备变量
        - 重写的query_list
        - 重写的keywords_list，尽管不一定用到
        - 向量化的query_list
        """
        self.rewritten_query_list = pipeline_context.rewritten_query_list
        self.rewrittem_keywords = pipeline_context.rewritten_keywords
        self.search_filter = self.retrival_module.filter_list2filter(pipeline_context.search_filter)

        if isinstance(self.rewritten_query_list, str):
            self.rewritten_query_list= [self.rewritten_query_list]
        logger.info(f'正在嵌入{json.dumps(self.rewritten_query_list,ensure_ascii=False,indent=4)}')
        (embedding_result_list,
        embedding_model,
        embedding_token_usage)= self.embedding_module.generate_embedding(
            query_or_querylist=self.rewritten_query_list,
            engine='large',
        )

        # 更新总用量
        pipeline_context.total_usage['embedding_usage'].setdefault(embedding_model,0)
        pipeline_context.total_usage['embedding_usage'][embedding_model] += embedding_token_usage
        
        """2. 四种检索
        分别是关键词搜索，向量搜索,与qa search和filesearch的组合
        """
        if self.search_filter:
            qa_search_filter = self.search_filter+' and filetype eq \'qa\' '
            file_search_filter = self.search_filter + ' and filetype ne \'qa\' '
        else :
            qa_search_filter = self.search_filter + 'filetype eq \'qa\' '
            file_search_filter = self.search_filter + 'filetype ne \'qa\' '
        
        # 线程池并发
        with ThreadPoolExecutor(max_workers=9) as executor:
        # 提交所有 search 任务到线程池中
            futures = [
                executor.submit(
                    self.retrival_module.search,
                    search_text_or_list=self.rewritten_query_list,
                    top=10,
                    filter=qa_search_filter,
                    select=['filename', 'tag', 'content', 'idfile']
                ),
                executor.submit(
                    self.retrival_module.search,
                    search_text_or_list='',
                    vector_or_list=embedding_result_list,
                    top_k=10,
                    filter=qa_search_filter,
                    vector_fields='contentVector',
                    select=['filename', 'tag', 'content', 'idfile']
                ),
                executor.submit(
                    self.retrival_module.search,
                    search_text_or_list=self.rewritten_query_list,
                    top=10,
                    filter=file_search_filter,
                    select=['filename', 'tag', 'content', 'idfile']
                ),
                executor.submit(
                    self.retrival_module.search,
                    search_text_or_list='',
                    vector_or_list=embedding_result_list,
                    top_k=10,
                    filter=file_search_filter,
                    vector_fields='contentVector',
                    select=['filename', 'tag', 'content', 'idfile']
                )
            ]

        # 获取并处理每个任务的结果
        results = []
        for future in as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                logger.error(f"多线程任务异常：{e}")
        
        # 解包结果到对应的变量
        qa_keywords_search_result_list, qa_vector_search_result_list, file_search_result_list, file_vector_search_result_list = results

        conbined_search_result = {
            'qa_keywords_search_result_list': qa_keywords_search_result_list,
            'qa_vector_search_result_list': qa_vector_search_result_list,
            'file_search_result_list': file_search_result_list,
            'file_vector_search_result_list': file_vector_search_result_list
        }

        """3. 过滤"""
        logger.info('开始过滤')
        filtered_search_result_list,filter_model,filter_token_usage = self.filter_module.filter(
            query=self.rewritten_query_list,
            search_result_list=conbined_search_result
        )
        logger.debug(f'\n过滤完成\nfrom:{json.dumps(conbined_search_result,ensure_ascii=False,indent=4)}\nto:{json.dumps(filtered_search_result_list,ensure_ascii=False,indent=4)}')
        pipeline_context.total_usage['chat_usage'].setdefault(filter_model,0)
        pipeline_context.total_usage['chat_usage'][filter_model] += filter_token_usage


        """4. 单文件深度搜索"""
        logger.info('开始单文件深度搜索')
        with ThreadPoolExecutor(max_workers=9) as executor:
            futures = [
                executor.submit(
                    self.retrival_module.search,
                    search_text_or_list=self.rewritten_query_list,
                    vector_or_list=embedding_result_list,
                    top=2,
                    filter=(self.search_filter+' and ' if self.search_filter else '')+f'(idfile eq \'{filtered_search_result["idfile"]}\') ' ,
                    vector_fields='contentVector',
                    query_type='semantic',
                    query_language='zh-cn',
                    semantic_configuration_name='my-semantic-config',
                    select=['filename','tag','content','idfile'], 
                ) for filtered_search_result in filtered_search_result_list
            ]
        # 收集结果
        final_search_results = []
        for future in as_completed(futures):
            try:
                final_search_results += future.result()
            except Exception as e:
                logger.error(f'单文件深度搜索出错: {e}')

        logger.debug(f'最终搜索结果: {json.dumps(final_search_results,ensure_ascii=False,indent=4)}')
        return final_search_results


class SearchSingleCorpusPipeline(BasePipline):
    def __init__(
            self,
            azure_openai_api_key: str,
            azure_openai_api_base: str,
            azure_openai_api_version: str,
            search_api_endpoint: str,
            search_api_key: str,
            search_api_index_name: str,
    ):
        self.azure_openai_api_key = azure_openai_api_key
        self.azure_openai_api_base = azure_openai_api_base
        self.azure_openai_api_version = azure_openai_api_version
        self.search_api_endpoint = search_api_endpoint
        self.search_api_key = search_api_key
        self.search_api_index_name = search_api_index_name

        self.retrival_module = AzureAISearchRetrivalModule(
            endpoint=self.search_api_endpoint,
            key=self.search_api_key,
            index_name=self.search_api_index_name,
        )
        
        embedding_config = config.get('other_config',{}).get('embedding_config',{})
        if embedding_config:
            logger.debug(f'using special embedding config')
            self.embedding_module = AzureOpenAIEmbeddingModule(
                api_base=embedding_config.get('azure_openai_api_base'),
                api_key=embedding_config.get('azure_openai_api_key'),
                api_version=embedding_config.get('azure_openai_api_version'),
            )
        else:
            logger.error('未找到embedding配置')
            exit(1)


    def run(
            self,
            pipeline_context: PipelineContext,
    ):
        rewrite_query_list = pipeline_context.rewritten_query_list
        search_filter = pipeline_context.search_filter

        if isinstance(rewrite_query_list,str):
           rewrite_query_list = [rewrite_query_list]

        # 1. 嵌入
        logger.info(f'正在嵌入\n{json.dumps(rewrite_query_list,ensure_ascii=False,indent=4)}')
        self.embedding_result_list, self.embedding_model, self.embedding_token_usage = self.embedding_module.generate_embedding(
            query_or_querylist=rewrite_query_list,
            engine='large',
        )
        pipeline_context.total_usage['embedding_usage'].setdefault(self.embedding_model,0)
        pipeline_context.total_usage['embedding_usage'][self.embedding_model] += self.embedding_token_usage

        # 2. 搜索
        self.filter_string = self.retrival_module.filter_list2filter(search_filter)
        search_result_list = self.retrival_module.search(
            search_text_or_list=rewrite_query_list,
            top=5,
            filter=self.filter_string,
            vector_fields='contentVector',
            vector_or_list=self.embedding_result_list,
            query_type='semantic',
            query_language='zh-cn',
            semantic_configuration_name='my-semantic-config',
            select=['filename','tag','content','idfile'],
        )

        logger.debug(f'搜索结果: {json.dumps(search_result_list,ensure_ascii=False,indent=4)}')
        return search_result_list


class MainRAGPipeline(BasePipline):
    """
    **项目主 RAG 流程**

    该流程是一个基于 RAG（检索增强生成）系统的主流程，控制 LLM（大语言模型）进行迭代调用，直到任务完成。

    **流程概述**

    整个流程由 LLM 主导，根据用户输入的查询（query）、筛选条件（filter）等变量，逐步进行以下决策和操作：

    1. **LLM 根据用户输入评估并确定搜索类型：**

        1. 全文搜索
        2. 单文件搜索
        3. 网络搜索

    2. **根据评估结果，调用相应的 Pipeline 或模块：**

        - 如果是全文搜索，则调用 `SearchAllCorpusPipeline`。
        - 如果是单文件搜索，则调用 `SearchSingleCorpusPipeline`。
        - 如果是网络搜索，则调用 `WebSearchModule`。

    3. **搜索完成后，LLM 判断是否需要进行下一轮迭代：**

        - 如果需要，流程会继续循环，直到完成任务。

    **项目中使用的模块和 Pipeline**

    以下是该项目中使用的主要模块和 Pipeline：

    - `self.generation_module` -> `AzureOpenAIGenerationModule`（生成模块）
    - `self.search_all_corpus_pipeline` -> `SearchAllCorpusPipeline`（全文搜索 Pipeline）
    - `self.search_singlefile_pipeline` -> `SearchSingleCorpusPipeline`（单文件搜索 Pipeline）
    - `self.web_search_module` -> `TavilySearchModule`（网络搜索模块）

    **SearchAllCorpusPipeline 中包含的模块：**

    - `embedding_module` -> `AzureOpenAIEmbeddingModule`（向量嵌入模块）
    - `retrival_module` -> `AzureOpenAIRetrivalModule`（检索模块）
    - `filter_module` -> `AzureOpenAIFilterModule`（筛选模块）

    **SearchSingleCorpusPipeline 中包含的模块：**

    - `embedding_module` -> `AzureOpenAIEmbeddingModule`（向量嵌入模块）
    - `retrival_module` -> `AzureOpenAIRetrivalModule`（检索模块）

    **关键点总结：**

    - 系统根据用户输入智能决策，选择相应的 Pipeline 或模块来执行任务。
    - 每个 Pipeline 由多个关键模块组成，分别负责不同阶段的搜索和检索任务。
    """


    def __init__(
            self,
            azure_openai_api_key: str,
            azure_openai_api_base: str,
            azure_openai_api_version: str,
            azure_openai_api_engine: str,
            search_api_key: str,
            search_api_index_name: str,
            search_api_endpoint: str,
            websearch_api_key: str,
            tools:List,
            prompt:List
    ):
        self.azure_openai_api_key = azure_openai_api_key
        self.azure_openai_api_base = azure_openai_api_base
        self.azure_openai_api_version = azure_openai_api_version
        self.azure_openai_api_engine = azure_openai_api_engine
        self.search_api_key = search_api_key
        self.search_api_index_name = search_api_index_name
        self.search_api_endpoint = search_api_endpoint
        self.websearch_api_key = websearch_api_key
        self.prompt = prompt
        self.tools = tools

        self.local_context  = threading.local()

        # 初始化各个Pipeline以及模块

        self.generation_module = AzureOpenAIGenerationModule(
            api_key = self.azure_openai_api_key,
            api_base = self.azure_openai_api_base,
            api_version = self.azure_openai_api_version,
        )

        self.search_all_pipeline = SearchAllCorpusPipeline(
            azure_openai_api_base=self.azure_openai_api_base,
            azure_openai_api_key=self.azure_openai_api_key,
            azure_openai_api_version=self.azure_openai_api_version,
            search_api_endpoint=self.search_api_endpoint,
            search_api_key=self.search_api_key,
            search_api_index_name=self.search_api_index_name,
        )

        self.search_singlefile_pipeline = SearchSingleCorpusPipeline(
            azure_openai_api_base=self.azure_openai_api_base,
            azure_openai_api_key=self.azure_openai_api_key,
            azure_openai_api_version=self.azure_openai_api_version,
            search_api_key=self.search_api_key,
            search_api_index_name=self.search_api_index_name,
            search_api_endpoint=self.search_api_endpoint
        )

        self.websearch_module = TavilyWebSearchModule(
            api_key=self.websearch_api_key
        )
        logger.info(f'初始化完成\n')

    def run(
            self,
            query: str,
            dialog_history: List[Dict] = [],
            search_filter: Dict = {},
    )->Tuple[Dict,Dict]:
        """
        执行整个RAG流程，包括生成、搜索、过滤等步骤。

        Args:
            query (str): 用户查询
            dialog_history (List[Dict]): 对话历史记录
            search_filter (Dict): 搜索过滤条件

        Returns:
            self.answer (Dict): 回答结果,格式为

                {
                    'answer':'xxx',
                    'references':[
                        {
                            'idfile':'xxx',
                            'filename':'xxx',
                            'tag':'xxx',
                        },
                        ...
                    ],
                }

            context.total_usage (Dict): 总计费情况,格式为

                {
                    'chat_usage':{
                        'gpt35turbo':xxx,
                        'gpt4':xxx,
                        ...
                    },
                    'embedding_usage':{
                        'text-embedding-3-large':xxx,
                        ...
                    },
                }
        """
        logger.info(f'开始执行RAG')
        self.local_context.context = PipelineContext()
        context = self.local_context.context
        context.dialog_history = self.prompt+dialog_history+[{'role': 'user', 'content': query}]
        context.search_filter = search_filter

        json_decode_error_count = 0
        while True:
            response = self.generation_module.generate(
                engine = self.azure_openai_api_engine,
                messages = context.dialog_history,
                tools = self.tools,
                response_format = {'type':'json_object'},
            )
            context.dialog_history.append(response.choices[0].message.to_dict_recursive())
            context.total_usage['chat_usage'].setdefault(response.model, 0)
            context.total_usage['chat_usage'][response.model] += response.usage.total_tokens

            if 'tool_calls' in response.choices[0].message:
                for i,tool_call_message in enumerate(response.choices[0].message.get('tool_calls'),start=1):
                    logger.info(f'gpt将调用函数,当前为第{i}个,共{len(response.choices[0].message["tool_calls"])}个')
                    function_name=tool_call_message['function']['name']
                    function_parameters=json.loads(tool_call_message['function']['arguments'])
                    function_id=tool_call_message['id']
                    function_call_info=f'''
                    function_name         :{function_name}
                    function_parameters   :{function_parameters}
                    function_id           :{function_id}'''
                    logger.info(f'gpt调用函数信息：{function_call_info}')

                    if function_name == 'search_all_corpus':
                        context.rewritten_query_list = function_parameters['rewritten_query_list']
                        context.rewritten_keywords = function_parameters['rewritten_keywords']
                        if context.search_filter.get('idfile',None) == None or context.search_filter.get('idfile') == []:
                            logger.info(f'将在所有文件中搜索')
                            tool_content = str(self.search_all_pipeline.run(
                                pipeline_context=context,
                            ))
                        else:
                            logger.info(f'将在单个文件中搜索')
                            tool_content = str(self.search_singlefile_pipeline.run(
                                pipeline_context=context,
                            ))
                    elif function_name == 'websearch':
                        logger.info(f'将在网络上搜索')
                        tool_content = str(self.websearch_module.websearch(query=function_parameters['query']))

                    context.dialog_history.append(
                        {
                            "role": "tool",
                            'tool_call_id':function_id,
                            'name':function_name,
                            'content':tool_content
                        }
                    )
                    logger.info(f'函数调用完毕')
            else:
                try:
                    self.answer = json.loads(response.choices[0].message.content)
                except JSONDecodeError:
                    json_decode_error_count += 1
                    if json_decode_error_count <=3:
                        context.dialog_history.pop()
                        logger.error(f'gpt返回结果无法解析为json,正在重试,当前为第{json_decode_error_count}次')
                        continue
                    else :
                        raise Exception(f'gpt返回结果无法解析为json,已重试{json_decode_error_count}次,请检查gpt返回结果')
                return self.answer, context.total_usage


main_rag_pipeline = MainRAGPipeline(**config['main_rag_pipeline_config'])