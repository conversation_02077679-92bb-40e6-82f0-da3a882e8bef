#!/usr/bin/python3
# -*- coding: utf-8 -*-

import os
import re
import logging
import json
import sys
import datetime
import uvicorn

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from anyio.lowlevel import RunVar
from anyio import CapacityLimiter
from contextlib import asynccontextmanager

sys.path.append(os.path.dirname(__file__) + "/..")
sys.path.append("/mnt/datadisk1/deployment/chatbot")

from askdoc.utils.searchstep4 import update_endtime
from askdoc.pipelines.search_corpus_pipeline import main_rag_pipeline

logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
handler.setFormatter(
    logging.Formatter(
        "[%(asctime)s] [%(levelname)s] %(filename)s:%(lineno)d - %(message)s"
    )
)
logger.addHandler(handler)
logger.setLevel(logging.INFO)


@asynccontextmanager
async def lifespan(app: FastAPI):
    RunVar("_default_thread_limiter").set(CapacityLimiter(200))
    yield


app = FastAPI(lifespan=lifespan)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 可以是您允许的来源列表
    allow_credentials=True,
    allow_methods=["*"],  # 可以是您允许的HTTP方法列表
    allow_headers=["*"],  # 可以是您允许的请求头列表
)


def parse_context(context: str):
    """
    传入的context为字符串, 格式为：
    USER QUESTION: XXXX
    ASSISTANT ANSWER: XXX
    USER QUESTION: XXX
    ASSISTANT ANSWER: XXX
    ....
    要解析成字典列表格式，也就是：
    [{'role': 'user', 'contenxt': 'XXXX'},
     {'role': 'assistant', 'contenxt': 'XXX'},
     {'role': 'user', 'contenxt': 'XXX'},
     {'role': 'assistant', 'contenxt': 'XXX'}]
    """
    # 正则表达式匹配模式
    pattern = re.compile(
        r"(USER QUESTION:|ASSISTANT ANSWER:)\s*(.+?)(?=\nUSER QUESTION:|\nASSISTANT ANSWER:|$)",
        re.DOTALL,
    )

    matches = pattern.findall(context)

    # 构造字典列表
    conversation = []
    for match in matches:
        role = "user" if match[0] == "USER QUESTION:" else "assistant"
        content = match[1] if role == "user" else match[1][1:]
        conversation.append({"role": role, "content": content})

    return conversation


@app.post("/chatHTTP")
def receive(data: dict):
    """
    :param data:
    :return:
    """
    logger.info(f"收到数据:{json.dumps(data,ensure_ascii=False,indent=4)}")

    folder_id_list = data["folder-ids"].split(",") if "folder-ids" in data else []
    corpus_id_list = data.get("corpus-id", [])
    if corpus_id_list == "-1":
        corpus_id_list = []
    if isinstance(corpus_id_list, str):
        corpus_id_list = [corpus_id_list]
    dialog_history = parse_context(data.get("context", ""))
    query = data["question"]

    answer, usage = main_rag_pipeline.run(
        query=query,
        dialog_history=dialog_history,
        search_filter={
            "folder_id": folder_id_list,
            "idfile": corpus_id_list,
            "endtime": f"((endtime gt '{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}') or (endtime eq ''))",
        },
    )

    references = answer["references"]
    if references:
        references_formated = (
            "、".join(
                [item["tag"] + " file_id:" + item["idfile"] for item in references]
            )
            + "。"
        )
    else:
        references_formated = ""
    usage_formated = 0
    usage_chat = (
        "/model:"
        + list(usage["chat_usage"].keys())[0]
        + "/tokens:"
        + str(list(usage["chat_usage"].values())[0])
    )
    if usage["embedding_usage"]:
        usage_embedding = (
            "/embeddings_model:"
            + "large"
            + "/embeddings_tokens:"
            + str(list(usage["embedding_usage"].values())[0])
        )
    else:
        usage_embedding = "/embeddings_model:large/embeddings_tokens:0"
    usage_formated = usage_chat + usage_embedding
    interval = "INTERVAL"

    result = answer["answer"] + "\n\n" + references_formated + interval + usage_formated
    logger.info(f"result:\n{result}")
    return result


@app.post("/UpdateEndtime")
def UpdatEndtime(data: dict):
    """
    data参数：
        folder_id_list: 文件夹路径
        file_id_list: 文件ID
        endtime: 失效时间
    """
    folder_id_list = data["folder_id_list"].split(",")
    file_id_list = data["file_id_list"].split(",")
    endtime = data["endtime"]
    # 输出上面参数的type（）

    try:
        update_endtime(folder_id_list=folder_id_list, file_id_list=file_id_list,endtime=endtime)
    except Exception as e:
        print(e)
        raise HTTPException(status_code=404, detail="更新失败")
    return f"更新过期时间成功"


if __name__ == "__main__":
    uvicorn.run("chat:app", host="0.0.0.0", port=80, workers=8)