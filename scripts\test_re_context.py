import re

def parse_context(context:str):
    '''
    传入的context为字符串, 格式为：
    USER QUESTION: XXXX
    ASSISTANT ANSWER: XXX
    USER QUESTION: XXX
    ASSISTANT ANSWER: XXX
    ....
    要解析成字典列表格式，也就是：
    [{'role': 'user', 'contenxt': 'XXXX'},
     {'role': 'assistant', 'contenxt': 'XXX'},
     {'role': 'user', 'contenxt': 'XXX'},
     {'role': 'assistant', 'contenxt': 'XXX'}]
    '''
     # 正则表达式匹配模式
    pattern = re.compile(r'(USER QUESTION:|ASSISTANT ANSWER:)\s*(.+?)(?=\nUSER QUESTION:|\nASSISTANT ANSWER:|$)', re.DOTALL)
    
    matches = pattern.findall(context)
    
    # 构造字典列表
    conversation = []
    for match in matches:
        role = 'user' if match[0] == 'USER QUESTION:' else 'assistant'
        content = match[1] if role == 'user' else match[1][1:]
        conversation.append({'role': role, 'content': content})
    
    return conversation


context='''
USER QUESTION:酸奶样品，测试脂肪，GB5009.5-2016第二法和第三法哪个比较适用a;sdfja;lsfjdka
ASSISTANT ANSWER:"对不起，没有在语料中找到相关的文件
USER QUESTION:酸奶样品，测试脂肪，GB5009.5-2016第二法和第三法哪个比较适用
ASSISTANT ANSWER:"对不起，没有在语料中找到相关的文件
USER QUESTION:酸奶样品，测试脂肪，GB5009.5-2016第二法和第三法哪个比较适用
ASSISTANT ANSWER:"对不起，没有在语料中找到相关的文件
'''

print(parse_context(context))
[print(i) for i in parse_context(context)]