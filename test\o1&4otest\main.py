"""
测试不同模型花费
"""
import aiohttp
import asyncio
import json
import time
import datetime

# Define the endpoint URL
url = "http://127.0.0.1:80/chatHTTP"

# Define the test queries
test_data = [
    {"question": "茶叶在欧盟草甘膦的限值是多少？", "context": "", "corpus-id": []},
    {
        "question": "饲料GAP良好农业规范认证需要测试那些项目",
        "context": "",
        "corpus-id": [],
    },
    {"question": "欧盟葡萄柚的农残限量要求", "context": "", "corpus-id": []},
    {
        "question": "出口欧洲的辣椒的矮壮素的限值 是多少？",
        "context": "",
        "corpus-id": [],
    },
    {"question": "食用淀粉中铅的限值是多少", "context": "", "corpus-id": []},
    {
        "question": "在GB 2762-2022 食品安全国家标准 食品中污染物限量.pdf中，酸奶的铅限量是多少？",
        "context": "",
        "corpus-id": [],
    },
    {
        "question": "样品是发酵乳，根据GB 2762-2022，有限值的项目都有哪些，限值分别是多少？",
        "context": "",
        "corpus-id": [],
    },
    {"question": "酱油 铅的限值是多少", "context": "", "corpus-id": []},
    {
        "question": "鲜冻猪肉 镉含量南京测试的周期，方法和费用",
        "context": "",
        "corpus-id": [],
    },
    {
        "question": "冻片猪肉挥发性盐基氮,mg/100g宁波的测试周期、价格、方法",
        "context": "",
        "corpus-id": [],
    },
    {
        "question": "鲜冻牛肉的铅武汉的测试周期、价格、方法",
        "context": "",
        "corpus-id": [],
    },
    {
        "question": "鸡肉的菌落总数青岛的测试周期、价格、方法",
        "context": "",
        "corpus-id": [],
    },
]


# Function to send a single request
async def send_request(session, idx, query):
    start_time = time.time()
    try:
        async with session.post(url, json=query) as response:
            response_time = time.time() - start_time
            if response.status == 200:
                return {
                    "query_index": idx + 1,
                    "question": query["question"],
                    "response_time": response_time,
                    "response": await response.text(),
                }
            else:
                return {
                    "query_index": idx + 1,
                    "question": query["question"],
                    "response_time": response_time,
                    "error": f"HTTP {response.status}: {await response.text()}",
                }
    except Exception as e:
        return {
            "query_index": idx + 1,
            "question": query["question"],
            "response_time": None,
            "error": str(e),
        }


# Main function to run all requests in parallel
async def main():
    async with aiohttp.ClientSession() as session:
        tasks = [
            send_request(session, idx, query) for idx, query in enumerate(test_data)
        ]
        results = await asyncio.gather(*tasks)

    # Save results to a JSON file
    cur_time = datetime.datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    with open(f"test_results-{cur_time}.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=4)

    print("Test completed. Results saved to 'test_results.json'.")


# Run the script
if __name__ == "__main__":
    asyncio.run(main())
