import random
import string
import time

import PyPDF2
import json


def extract_pdf_content(pdf_file_path):
    pdf_data = []

    with open(pdf_file_path, 'rb') as pdf_file:
        pdf_reader = PyPDF2.PdfReader(pdf_file)

        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            page_content = page.extract_text().strip()
            line={'id':str(page_num + 1),'content':page_content}
            pdf_data.append(line)
    return pdf_data


def generate_random_string(length=20):
    characters = string.ascii_lowercase
    random_string = ''.join(random.choice(characters) for _ in range(length))


    timestamp = int(time.time())
    random_string_with_timestamp = random_string + str(timestamp)
    return random_string_with_timestamp


def save_to_json(data, json_file_path):
    with open(json_file_path, 'w', encoding='utf-8') as json_file:
        json.dump(data, json_file, ensure_ascii=False, indent=4)




if __name__ == "__main__":
    # pdf_file_path ='c:\脉搏传感器说明书.pdf'
    # json_file_path = "output112.json"
    #
    # pdf_data = extract_pdf_content(pdf_file_path)
    # save_to_json(pdf_data, json_file_path)
    length_of_random_string = 26
    random_string = generate_random_string(length_of_random_string)
    print(random_string)