'''
这是一个测试脚本，用于测试azuresearch的接口。将搜索到的指定文件的过期时间设定为自定义的时间。
使用方法：
1.修改filterlist_folder_id,filterlist_idfile,filterlist_filename, 以及endtime
2.运行脚本，查看结果。

脚本会依次输出搜索到的chunk以及相关信息、key、修改结果，每次都会提示输入回车继续。
'''
import logging
logger=logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
from azure.core.credentials import AzureKeyCredential
from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential

service_endpoint = "https://afaisearch.search.windows.net"
index_name = 'sgsaf'
key = "jXlotzlexllX10tW5cfxBlazrKrprNnDKS4xAy3ypYAzSeD0lqiS"

search_client = SearchClient(service_endpoint, index_name, AzureKeyCredential(key))

#--------------------------------------
endtime='2024-10-10 11:10:21'
filterlist_folder_id=[]
filterlist_idfile=[]
filterlist_filename=[]
#--------------------------------------


filter_folder_id=' or '.join([f'folder_id eq \'{i}\'' for i in filterlist_folder_id])
filter_idfile='or'.join([f'idfile eq \'{i}\'' for i in filterlist_idfile])
filter_filename='or'.join([f'filename eq \'{i}\'' for i in filterlist_filename])
filter_list=[]
filter_list.append(filter_folder_id)
filter_list.append(filter_idfile)
filter_list.append(filter_filename)
filter=''

first_flag=0
for i in filter_list:
    if i=='':
        continue
    else:
        if first_flag==0:
            filter=i
            first_flag=1
        else:
            filter=filter+' and '+i

logger.warning(f'please check your filter: {filter}')
input('input  to continue')


results = search_client.search(
    search_text="", 
    select=[
    "endtime","tag", "id","folder_id"
    ,"starttime","idfile",'filename','content',
    ],
    filter=filter)


i=0
key_list=[]
for num,result in enumerate(results) :
    i+=1
    [print('\033[1m',i,'\033[0m',':',result[i][:50]+'\033[1m...(remaining text omitted)\033[0m' if (type(result[i]) is str and len(result[i])>50) else result[i]) for i in result]
    print('-'*30+'\n')
    key_list.append(result['id'])
    if i%10==0:
        input('input  to continue')

# documents_to_update=[
#     {
#         'folder_id': 'test1',
#         'idfile': '1',
#         'filename': '计算机网络授课ppt',
#         'endtime': '2',
#     }
# ]

[print(i) for i in key_list]
input('input  to continue')

documents_to_update=[
    {
        'id':f'{i}',
        'endtime':endtime 
    } for i in key_list
]

response = search_client.merge_documents(documents=documents_to_update)
print(response)

for num,i in enumerate(response):
    print(f'response[{num}].succeeded:',i.succeeded)
