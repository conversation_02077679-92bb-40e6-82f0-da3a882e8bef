'''
这是searchstep4的update_endtime()函数的测试文件
'''

import logging
logging.basicConfig(level=logging.ERROR,format='%(asctime)s - %(levelname)s - %(message)s (%(filename)s:%(lineno)d)')
logger=logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
import traceback
from azure.core.credentials import AzureKeyCredential
from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential

service_endpoint = "https://afaisearch.search.windows.net"
index_name = 'sgsaf'
key = "jXlotzlexllX10tW5cfxBlazrKrprNnDKS4xAy3ypYAzSeD0lqiS"


def update_endtime(folder_id_list:list[str], filename_list:list[str], endtime:str):
    '''
    folder_id_list: 文件夹路径
    filename_list: 文件名
    endtime: 失效时间
    '''
    logger.debug(f'updating endtime to {endtime}')
    search_client = SearchClient(service_endpoint, index_name, AzureKeyCredential(key))
    filter_folder_id = ' or '.join([f"folder_id eq \'{i}\'" for i in folder_id_list])
    filter_filename = ' or '.join([f"filename eq \'{i}\'" for i in filename_list])
    filter_list=[]
    filter_list.append(filter_folder_id)
    filter_list.append(filter_filename)
    logger.debug(f'your filter_list is:')
    [print(i) for i in filter_list]

    filter=''
    first_flag=-1
    for num,i in enumerate(filter_list):
        if i == '':
            continue
        else:
            if first_flag == -1:
                filter = i
                first_flag=0
            else:
                filter = f'{filter} and {i}'

    logger.debug(f'your filter is {filter}')

    results = search_client.search(
        search_text="*", 
        filter=filter, 
        select=[
        "endtime","tag", "id","folder_id"
        ,"starttime","idfile",'filename','content'],
        )

    id_list=[]
    for num,result in enumerate(results):
        id_list.append(result['id'])
    logger.debug(f'search completed, got {len(id_list)} results')

    documents_to_udpate=[
        {
            'id': f'{i}',
            'endtime': endtime
        } for i in id_list
    ]

    try:
        response = search_client.merge_documents(documents=documents_to_udpate)
        for num,result in enumerate(response):
            if result.status_code != 200:
                logger.error(f'error in merge_documents: {result}')
                return 'error message'
            else:
                logger.debug(f'updated all documents successfully')
                return 'success message'
    except Exception as e:
        # 输出traceback信息
        traceback.print_exc()
        logger.error(f'error in merge_documents: {e}')
        return 'error message'


if __name__ == '__main__':
    filterlist_folder_id=['test1']
    filterlist_filename=['计算机网络授课ppt']
    update_endtime(filterlist_folder_id, filterlist_filename,'2021-08-31')