import pandas as pd
import os
import json
import traceback
import random
import string

def generate_random_string(length=26):
    characters = string.ascii_lowercase
    random_string = ''.join(random.choice(characters) for _ in range(length))


    # timestamp = time.time()
    random_string_with_timestamp = random_string
    return random_string_with_timestamp
def num_tokens_from_messages_csv(text):
    return len(text.split())

def save_to_json(data, json_file_path):
    os.makedirs(os.path.dirname(json_file_path), exist_ok=True)
    with open(json_file_path, 'w', encoding='utf-8') as json_file:
        json.dump(data, json_file, ensure_ascii=False, indent=4)
def read_multisheet_excel(file_path, filename='1', ct='0', et='0', fileid='1', indexName=111, folder_id='test1'):
    if indexName == 111:
        indexName = generate_random_string()
    max_tokens = 512
    print('--------开始处理Excel文件的多个Sheet-----------------')
    try:
        # 读取Excel文件的所有Sheet
        xls = pd.ExcelFile(file_path)

        for sheet_name in xls.sheet_names:
            print(f'处理Sheet: {sheet_name}')
            # 读取每个Sheet的数据
            sheet_data = pd.read_excel(xls, sheet_name=sheet_name)

            text_content = ''
            text_positions = []  # 用于存储每个文本片段的位置信息

            for row_idx, row in sheet_data.iterrows():
                for col_idx, cell in enumerate(row):
                    if pd.notna(cell):  # 跳过空值
                        text_content += str(cell)
                        text_positions.append((row_idx + 1, col_idx + 1))

            # 按照 tokens 分割文本
            pages_result = []
            temp_text = ""
            temp_tag = ""  # 用于暂存当前文本片段的位置信息
            for char_idx, char in enumerate(text_content):
                temp_text += char
                if num_tokens_from_messages_csv(temp_text) > max_tokens:
                    if char_idx < len(text_positions):
                        temp_tag = text_positions[char_idx]
                    else:
                        temp_tag = None

                    pages_result.append({'id': generate_random_string(), 'content': temp_text[:-1], 'tag': f"来源：{filename} Sheet:{sheet_name}", 'filename': filename, 'starttime': ct, 'endtime': et, 'idfile': fileid, 'folder_id': folder_id})
                    temp_text = char
                else:
                    if char_idx < len(text_positions):
                        temp_tag = text_positions[char_idx]
                    else:
                        temp_tag = None

            remaining_text = temp_text

            if remaining_text:
                last_position = text_positions[-1]
                pages_result.append({'id': generate_random_string(), 'content': remaining_text, 'tag': f"来源：{filename} Sheet:{sheet_name}", 'filename': filename, 'starttime': ct, 'endtime': et, 'idfile': fileid, 'folder_id': folder_id})

            # 保存每个Sheet的结果到JSON文件
            save_to_json(pages_result, f"data/jsonfile_{indexName}_{sheet_name}.json")

        print(f'indexName is {indexName}')
        return indexName

    except Exception as e:
        traceback.print_exc()
        print("Error processing Excel file:", str(e))


import os
import pandas as pd
import traceback

def read_multisheet_excel2(file_path, filename='1', ct='0', et='0', fileid='1', indexName=111, folder_id='test1'):
    if indexName == 111:
        indexName = generate_random_string()
    max_tokens = 512
    output_dir = "data"

    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} does not exist.")
        return

    # 检查并创建保存目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    print('--------开始处理Excel文件的多个Sheet-----------------')
    try:
        # 读取Excel文件的所有Sheet
        xls = pd.ExcelFile(file_path)

        for sheet_name in xls.sheet_names:
            print(f'处理Sheet: {sheet_name}')
            # 读取每个Sheet的数据
            sheet_data = pd.read_excel(xls, sheet_name=sheet_name)

            # 检查是否为空Sheet
            if sheet_data.empty:
                print(f"Warning: Sheet {sheet_name} is empty. Skipping.")
                continue

            text_content = ''.join([str(cell) for row in sheet_data.iterrows() for cell in row[1] if pd.notna(cell)])
            text_positions = [(row_idx + 1, col_idx + 1) for row_idx, row in sheet_data.iterrows() for col_idx, cell in enumerate(row) if pd.notna(cell)]

            pages_result = []
            temp_text = ""
            for char_idx, char in enumerate(text_content):
                temp_text += char
                if num_tokens_from_messages_csv(temp_text) > max_tokens:
                    temp_tag = text_positions[char_idx] if char_idx < len(text_positions) else None
                    pages_result.append({
                        'id': generate_random_string(),
                        'content': temp_text[:-1],
                        'tag': f"来源：{filename} Sheet:{sheet_name}",
                        'filename': filename,
                        'starttime': ct,
                        'endtime': et,
                        'idfile': fileid,
                        'folder_id': folder_id
                    })
                    temp_text = char

            # 处理剩余的文本
            if temp_text:
                last_position = text_positions[-1] if text_positions else None
                pages_result.append({
                    'id': generate_random_string(),
                    'content': temp_text,
                    'tag': f"来源：{filename} Sheet:{sheet_name}",
                    'filename': filename,
                    'starttime': ct,
                    'endtime': et,
                    'idfile': fileid,
                    'folder_id': folder_id
                })

            # 保存每个Sheet的结果到JSON文件
            try:
                json_file_path = os.path.join(output_dir, f"jsonfile_{indexName}_{sheet_name}.json")
                save_to_json(pages_result, json_file_path)
                print(f"Successfully saved {json_file_path}")
            except Exception as save_error:
                print(f"Error saving JSON for {sheet_name}: {save_error}")

        print(f'indexName is {indexName}')
        return indexName

    except Exception as e:
        traceback.print_exc()
        print("Error processing Excel file:", str(e))




import os
import json
import pandas as pd

def test_read_excel():
    # 创建一个临时Excel文件
    test_file = "/home/<USER>/jack/chatbot-alg/scripts/multisheet_test.xlsx"
    # test_data = {
    #     'Sheet1': {
    #         'Column1': [1, 2, 3],
    #         'Column2': ['A', 'B', 'C']
    #     },
    #     'Sheet2': {
    #         'Column1': [4, 5, 6],
    #         'Column2': ['D', 'E', 'F']
    #     }
    # }

    # 将数据保存为一个Excel文件
    # with pd.ExcelWriter(test_file) as writer:
    #     for sheet_name, data in test_data.items():
    #         df = pd.DataFrame(data)
    #         df.to_excel(writer, sheet_name=sheet_name, index=False)

    # 调用 read_excel 函数并捕获 indexName
    index_name = read_multisheet_excel2(test_file)

    # 验证是否生成了对应的JSON文件
    # for sheet_name in test_data.keys():
    #     json_file = f"data/jsonfile_{index_name}_{sheet_name}.json"
    #     assert os.path.exists(json_file), f"JSON file for {sheet_name} not found!"

    #     # 验证JSON文件的内容
    #     with open(json_file, 'r', encoding='utf-8') as f:
    #         content = json.load(f)

    #     assert isinstance(content, list), f"JSON content for {sheet_name} is not a list!"
    #     assert len(content) > 0, f"JSON content for {sheet_name} is empty!"
    #     assert "content" in content[0], f"JSON content for {sheet_name} missing 'content' key!"

    # print("所有测试通过！")
     # 使用 pandas 读取你的 Excel 文件来获取实际的 sheet 名称
    xls = pd.ExcelFile(test_file)
    sheet_names = xls.sheet_names  # 获取所有Sheet的名称
    for sheet_name in sheet_names:
        json_file = f"data/jsonfile_{index_name}_{sheet_name}.json"
        assert os.path.exists(json_file), f"JSON file for {sheet_name} not found!"

        # 验证JSON文件的内容
        with open(json_file, 'r', encoding='utf-8') as f:
            content = json.load(f)

        assert isinstance(content, list), f"JSON content for {sheet_name} is not a list!"
        assert len(content) > 0, f"JSON content for {sheet_name} is empty!"
        assert "content" in content[0], f"JSON content for {sheet_name} missing 'content' key!"

    print("所有测试通过！")

    # 清理生成的测试文件
    # os.remove(test_file)
    # for sheet_name in test_data.keys():
    #     json_file = f"data/jsonfile_{index_name}_{sheet_name}.json"
    #     if os.path.exists(json_file):
    #         os.remove(json_file)


# 调用测试函数
# test_read_excel()


import os
import json
import pytest
import pandas as pd





    # 本地的 Excel 文件路径
     # 替换为你的本地文件路径
def test_read_multisheet_excel():
    # 本地的 Excel 文件路径
    test_file = "/home/<USER>/jack/chatbot-alg/scripts/multisheet_test.xlsx" 

    # 调用待测函数
    index_name = read_multisheet_excel(test_file)

    # 验证结果
    output_dir = "data"
    assert index_name is not None, "Index name should not be None"

    # 使用 pandas 读取文件以获取实际的 sheet 名称
    xls = pd.ExcelFile(test_file)
    sheet_names = xls.sheet_names  # 获取所有 Sheet 的名称

    # 验证 JSON 文件的生成和内容
    for sheet_name in sheet_names:
        # 检查 Sheet 是否为空
        sheet_data = pd.read_excel(test_file, sheet_name=sheet_name)
        if sheet_data.empty:
            print(f"Skipping validation for empty sheet: {sheet_name}")
            continue  # 跳过对空 Sheet 的 JSON 文件验证

        # 验证非空 Sheet 的 JSON 文件
        json_file = os.path.join(output_dir, f"jsonfile_{index_name}_{sheet_name}.json")
        assert os.path.exists(json_file), f"JSON file for {sheet_name} not found!"

        # 检查 JSON 文件内容
        with open(json_file, 'r', encoding='utf-8') as f:
            content = json.load(f)

        assert isinstance(content, list), f"JSON content for {sheet_name} is not a list!"
        assert len(content) > 0, f"JSON content for {sheet_name} is empty!"
        assert "content" in content[0], f"JSON content for {sheet_name} missing 'content' key!"

    print("所有测试通过！")

    # 清理生成的 JSON 文件
    for sheet_name in sheet_names:
        json_file = os.path.join(output_dir, f"jsonfile_{index_name}_{sheet_name}.json")
        if os.path.exists(json_file):
            os.remove(json_file)



test_read_multisheet_excel()
