import sys
sys.path.append('./chatcode')

from askdoc.pipelines.search_corpus_pipeline import SearchAllCorpusPipeline

import openai
openai.api_type = 'azure'
openai.api_key = '********************************'
openai.api_base = 'https://af-eastus-aoai.openai.azure.com/'
openai.api_version = "2024-02-01"

import json

from tenacity import retry,stop_after_attempt, wait_fixed



def find_target_corpus(query:str):
    search_all_corpus_pipeline = SearchAllCorpusPipeline()
    filtered_result=search_all_corpus_pipeline.run(
        rewrite_query_list=query,
    )
    return filtered_result


if __name__ == '__main__':
    # 1. 读入文件answer.json,这是一个list，list每条包括 用户提问的问题，问题，目标语料
    import json
    with open("./test/answer_censored_filtered.json") as f:
        data = json.load(f)

    
    # 2.循环跑寻找语料函数，判断是否找到目标语料，并添加到json中
    record = []

    @retry(stop = stop_after_attempt(5),wait = wait_fixed(0.5))
    def for_loop(i,item):
        query = item['用户提问的问题']
        target_corpus = item['目标语料']

        found_courpus = find_target_corpus(query)
        
        prompt=[
            {'role':'system','content':f'请帮助我判断我的语料搜索服务是否正常，我的目标语料是{target_corpus},找到的结果是{found_courpus},我希望你返回这种类型的回答：'+r'{"found":True},或者{"found":False}'+'你应该以json格式返回.如果找到的结果中包含我的目标语料，你就应当返回{"found":True}.否则返回{"found":False}.'}
        ]
        response = openai.ChatCompletion.create(
        engine = '4o-mini',
        messages = prompt,
        response_format = {'type':'json_object'},
        )

        result=response.choices[0].message.content
        judge_result = json.loads(result)
        item['方案1结果'] = judge_result
        item['方案1结果']['found_results'] = found_courpus
        print(f'第{i}条结果：\n',item)
        record.append(item)

    for i, item in enumerate(data):
        for_loop(i,item)
        

    # 3. 记录结果，将结果写入到result.json文件中
    with open('./test/result_new1.json', 'w') as f:
        json.dump(record, f, indent=4,ensure_ascii=False)  