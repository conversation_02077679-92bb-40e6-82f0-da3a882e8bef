#!/usr/bin/python3
# -*- coding: utf-8 -*-
PROD_ENV=1

import os
import sys
import re
import logging
import traceback
import json

import requests
import time
import schedule
from lxml import etree

# 传输到askdoc的上一级目录下
sys.path.append('/mnt/datadisk1/deployment/chatbot')
sys.path.append(os.path.dirname(__file__) + "/../")
from database_ops import update_corpus_status
from pdf_processor import process_qa
from askdoc.utils.fileUtils import print_log

logger=logging.getLogger(name=__name__)
logger.setLevel(logging.DEBUG)

module_dir = os.path.dirname(os.path.abspath(__file__))
config_file_path = os.path.join(module_dir,'config.json')
with open(config_file_path, 'r',encoding='utf-8') as f:
    config_data = json.load(f)["getqa_config"]

params = config_data['params']
local_filename = config_data['local_filename']
loginurl = config_data['loginurl']
loginparams = config_data['loginparams']
orgsurl = config_data['orgsurl']
orgsparams = config_data['orgsparams']
filename_url = config_data['filename_url']
filename_params = config_data['filename_params']
xml_content = config_data['xml_content']
download_url = config_data['download_url']
download_params = config_data['download_params']
download_directory = config_data['download_directory']


def get_token(loginurl, loginparams):
    """
    获取token
    :param loginurl:
    :param loginparams:
    :return:
    """
    # 发送HTTP GET请求，带上参数
    response = requests.post(loginurl, params=loginparams)
    # 检查是否成功获取文件
    if response.status_code == 200:
        # 解码返回的内容
        decoded_content = response.content.decode()

        # 修复XML中的非法标签名
        fixed_content = decoded_content.replace('<q&a>', '<qa>').replace('</q&a>', '</qa>')

        # 解析修复后的XML内容
        root = etree.fromstring(fixed_content)

        # 查找<token>标签
        token_element = root.find('.//token')

        # 获取<token>标签的内容
        token_content = token_element.text if token_element is not None else None

        return token_content  # 输出token内容
    else:
        print_log(f"HTTP请求失败，状态码：{response.status_code}")


def get_orgs(orgsurl, orgsparams):
    """
    获取部门
    :param orgsurl:
    :param orgsparams:
    :return:
    """
    # 发送HTTP GET请求，带上参数
    response = requests.post(orgsurl, params=orgsparams)
    # 检查是否成功获取文件
    if response.status_code == 200:
        # 解码返回的内容
        decoded_content = response.content.decode()
        # 修复XML中的非法标签名
        fixed_content = decoded_content.replace('<q&a>', '<qa>').replace('</q&a>', '</qa>')

        # 解析修复后的XML内容
        root = etree.fromstring(fixed_content)

        orgs_element = root.find('.//ORG_ID')

        orgs = orgs_element.text if orgs_element is not None else None

        return orgs  # 输出token内容
    else:
        print_log(f"HTTP请求失败，状态码：{response.status_code}")


def get_file_info(filename_url, filename_params):
    """
    :param filename_url:
    :param filename_params:
    :return:
    """
    print_log(filename_url, filename_params)
    # 发送HTTP GET请求，带上参数
    response = requests.post(filename_url, params=filename_params)
    # 检查是否成功获取文件
    if response.status_code == 200:
        # 解码返回的内容
        decoded_content = response.content.decode()
        # 修复XML中的非法标签名
        fixed_content = decoded_content.replace('<q&a>', '<qa>').replace('</q&a>', '</qa>')

        # 解析修复后的XML内容
        # root = etree.fromstring(fixed_content)
        return fixed_content

    else:
        print_log(f"HTTP请求失败，状态码：{response.status_code}")


def get_corpus_ids(xml_content):
    """
    获取语料id
    :param xml_content:
    :return:
    """
    root = etree.fromstring(xml_content)

    corpus_ids_element = root.find('.//CORPUS_IDS')
    corpus_ids = corpus_ids_element.text if corpus_ids_element is not None else None

    return corpus_ids  # 输出token内容


def parse_xml_and_download(xml_content, download_url, download_params, download_directory):
    """
    解析xml，并下载
    :param xml_content:
    :param download_url:
    :param download_params:
    :param download_directory:
    :return:
    """
    # 解析XML内容
    root = etree.fromstring(xml_content)
    print_log(download_directory, download_url, download_params, root)

    files_info = []
    for page_data in root.xpath('.//pageData'):
        file_info = {}
        file_info['CREATE_TIME'] = page_data.findtext('CREATE_TIME')
        file_info['EXPIRE_TIME'] = page_data.findtext('EXPIRE_TIME', default='')  # 如果没有 EXPIRE_TIME 则置空
        file_info['ID'] = page_data.findtext('ID')
        file_info['NAME'] = page_data.findtext('NAME')
        #替换\r\n或者\n为空格
        file_info['NAME'] = file_info['NAME'].replace('\r\n', '\n').replace('\n', ' ')[:50] if file_info['NAME'] else file_info['NAME']        
        # print(type(file_info['NAME']))
        #用正则去掉filename中不能使用的字符
        if file_info['NAME']:
            # print(type(file_info['NAME']))
            file_info['NAME'] = re.sub('[\/:*?"<>|]', '', file_info['NAME'])
        logger.debug(f"file_info[name]: {file_info['NAME']},type of file_info[name]: {type(file_info['NAME'])}")
        file_info["CONTENT"] = page_data.find('CONTENT')
        # 检查FOLDERID键并处理
        folder_id = page_data.findtext('CATEGORY')

        if folder_id is not None:
            file_info['CATEGORY'] = folder_id
            logger.warning('got key: CATEGORY')
        else:
            logger.warning('CATEGORY key is missing')

        # 构造下载文件的URL和参数
        download_params['FILE_ID'] = file_info['ID']
        download_file_url = download_url
        download_file_params = download_params

        if file_info['NAME'] is not None:
            # 构造本地文件名
            local_filename = os.path.join(download_directory, file_info['NAME'])

            # 确保目录存在
            os.makedirs(os.path.dirname(local_filename), exist_ok=True)

            # 下载文件
            logger.debug(f'start downloading file, download_file_url: {download_file_url}, download_file_params: {download_file_params}, local_filename: {local_filename}')
            download_file(download_file_url, download_file_params, local_filename)

            # 添加文件路径到字典
            file_info['FILE_PATH'] = local_filename

            files_info.append(file_info)
            logger.debug(f'downloaded file: {local_filename}')

    return files_info


def get_qa(xml_content):
    """
    获取问答对
    :param xml_content:
    :return:
    """
    root = etree.fromstring(xml_content)

    qa_elements = root.findall('.//CONTENT')
    qa_contents = [elem.text for elem in qa_elements]

    return qa_contents


def download_file(url, params, local_filename):
    """
    下载文件
    :param url:
    :param params:
    :param local_filename:
    :return:
    """
    response = requests.get(url, params=params, stream=True)
    os.makedirs("downloads", exist_ok=True)
    # 检查是否成功获取文件
    if response.status_code == 200:
        # 打开本地文件，以二进制写入模式
        with open(local_filename, 'wb') as f:
            # 分块写入文件
            for chunk in response.iter_content(chunk_size=1024):
                f.write(chunk)
    else:
        print_log(f"HTTP请求失败，状态码：{response.status_code}")


def main():
    print_log('执行 getqa.py 中的 main 方法')
    token = get_token(loginurl, loginparams)

    orgsparams["token"] = token

    orgs = get_orgs(orgsurl, orgsparams)
    
    filename_params["token"] = token
    filename_params["ORG_ID"] = orgs
    download_params["token"] = token
    download_params["ORG_ID"] = orgs
    xml_content = get_file_info(filename_url, filename_params)
    print_log(xml_content)

    files_infos = parse_xml_and_download(xml_content, download_url, download_params, download_directory)
    # print_log(files_infos)

    qa_contents = get_qa(xml_content)
    for i, file_info in enumerate(files_infos):
        try:
            file_path = file_info['FILE_PATH']
            file_name = file_info['NAME']
            file_id = file_info['ID']
            creat_time = file_info['CREATE_TIME']
            end_time = file_info['EXPIRE_TIME']
            folder_id=file_info['CATEGORY']
            content = qa_contents[i]
            #file_extension = file_path.split('.')[-1].lower()
            #print(file_name)
            logger.debug(f'updating corpus status, file_id: {file_id}, EMBEDDING_STATUS: 2')
            update_corpus_status(ID=file_id, EMBEDDING_STATUS=2)
            print_log(content)
            process_qa(content, file_name, creat_time, end_time, file_id,folder_id=folder_id)
            logger.debug(f'updating corpus status, file_id: {file_id}, EMBEDDING_STATUS: 1')
            update_corpus_status(ID=file_id, EMBEDDING_STATUS=1)
        except Exception as e:
            print_log(f"发生错误: {e}")
            traceback.print_exc()
            update_corpus_status(ID=file_id, EMBEDDING_STATUS=-1)

    return files_infos


# main()
if __name__ == '__main__':
    # main()
    # 设置任务在每五分钟执行一次
    if PROD_ENV==1:
        schedule.every(5).minutes.do(main) 
        # 主循环，用于定时运行任务 
        while True: 
            schedule.run_pending() 
            time.sleep(1)
    else:
        while True:
            input(f'Press Enter to continue...')
            main()