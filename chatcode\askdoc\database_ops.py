# database_ops.py
import os
import json
import logging
logger=logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
import mysql.connector
from mysql.connector import Error
from datetime import datetime
from tenacity import retry, stop_after_delay, wait_fixed

DB_CONFIG_PATH = os.path.join(os.path.dirname(__file__),'config.json')
with open(DB_CONFIG_PATH, 'r',encoding='utf-8') as f:
    db_config = json.load(f)["database_config"]
logger.info(f'db_config: {db_config}')

@retry(stop=stop_after_delay(3), wait=wait_fixed(3))
def get_database_connection():
    """获取数据库连接"""
    try:
        logger.debug(f'connecting to database ')
        connection = mysql.connector.connect(
            host=db_config['host'],
            port=3306,
            user=db_config['user'],
            password=db_config['pwd'],
            database=db_config['db'],
            ssl_disabled=True,
            use_unicode=True,
            charset='utf8'
        )
        if connection.is_connected():
            logger.debug(f'connected to database ')
            return connection
    except Error as e:
        print("Error while connecting to MySQL", e)


def update_corpus_status(ID, EMBEDDING_STATUS):
    """更新用户的嵌入状态"""
    try:
        connection = get_database_connection()
        cursor = connection.cursor()

        # 查询 CATEGORY 字段
        query = "SELECT CATEGORY, MESSAGE_ID FROM dv_chat_corpus WHERE ID = %s"
        cursor.execute(query, (ID,))
        result = cursor.fetchone()
        
        if result:
            category, message_id = result
            if category == "ADD" and EMBEDDING_STATUS==1:
                # 更新 dv_chat_message 表中的 IS_OK 字段
                update_message_status(message_id, 2)

        # 更新 dv_chat_corpus 表中的 EMBEDDING_STATUS 字段
        query = "UPDATE dv_chat_corpus SET EMBEDDING_STATUS = %s WHERE ID = %s"
        cursor.execute(query, (EMBEDDING_STATUS, ID))
        connection.commit()
        print("Corpus updated successfully")
    except Error as e:
        print("Failed to update corpus:", e)
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("MySQL connection is closed")

def update_message_status(message_id, is_ok):
    """更新 dv_chat_message 表中的 IS_OK 字段"""
    try:
        connection = get_database_connection()
        cursor = connection.cursor()
        query1 = "UPDATE dv_chat_message SET IS_OK = %s WHERE ID = %s"
        cursor.execute(query1, (is_ok, message_id))

        query2="select QNADOUBLE from dv_chat_message where ID=%s"
        cursor.execute(query2, (message_id,))
        result = cursor.fetchone()
        logger.debug(f'result: {result},type of result: {type(result)}')
        message_id_q = result[0]

        query3="update dv_chat_message set IS_OK=%s where ID=%s"
        cursor.execute(query3, (is_ok,message_id_q))

        connection.commit()
        print("Message status updated successfully")
    except Error as e:
        print("Failed to update message status:", e)
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("MySQL connection is closed")

def is_expire(ID):
    """判断该ID是否过期"""
    try:
        connection = get_database_connection()
        cursor = connection.cursor()
        query = "SELECT EXPIRE_TIME FROM dv_chat_corpus WHERE ID = %s"
        cursor.execute(query, (ID,))
        result = cursor.fetchone()
        
        if result:
            expire_time = result[0]
            current_time = datetime.now()
            
            if expire_time < current_time:
                return 1
            else:
                return 0
        else:
            print("No record found with the given ID")
            return None
    except Error as e:
        print("Failed to check expiration:", e)
        return None
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("MySQL connection is closed")


def update_embedding_token_count(ID,embedding_token):
    # 修改消耗数据库
    try:
        connection = get_database_connection()
        cursor = connection.cursor()
        # query = "SELECT EXPIRE_TIME FROM dv_chat_corpus WHERE ID = %s"
        query0="SELECT ORG_ID FROM dv_chat_corpus where ID=%s"
        cursor.execute(query0, (ID, ))
        result = cursor.fetchone()
        logger.debug(f'result: {result},type of result: {type(result)}')
        org_id=result[0]
        logger.debug(f'org_id: {org_id},type of org_id: {type(org_id)}')


        query1 = "INSERT INTO dv_corpus_embedding (CREATE_TIME, CORPUS_ID, USED_TOKENS,ORG_ID) VALUES(%s, %s, %s,%s)"
        CREATE_TIME=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cursor.execute(query1, (CREATE_TIME,ID,embedding_token,org_id))

        query2="UPDATE dv_org set USED_TOKENS=USED_TOKENS+%s where ID=%s"
        cursor.execute(query2, (embedding_token,org_id))
        connection.commit()
        print("Emdedding count updated successfully")
    except Error as e:
        print("Failed to update:", e)
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("MySQL connection is closed")


# 更多数据库操作函数可以在这里定义
