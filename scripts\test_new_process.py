'''
新rag流程测试脚本。优化rag流程，增加函数接口
'''

#!/usr/bin/python3
# -*- coding: utf-8 -*-
import textwrap
import re
import logging
logger=logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
import json
import sys
sys.path.append('/home/<USER>/jack/chatbot-alg/chatcode')

# 要引用到askdoc的上一级路径
sys.path.append('/mnt/datadisk1/deployment/chatbot')
from askdoc.utils.searchstep4 import searchIndex,searchIndex_filename

import json
import openai

openai.api_type = "azure"
openai.api_key = "********************************"
openai.api_base = "https://af-eastus-aoai.openai.azure.com/"
openai.api_version ="2024-02-01"

tools = [
    {
        "type": "function",
        "function": {
            "name": "searchSingleFile",
            "description": "这是公司内部的存放标准文件的数据库的一个接口函数,用于搜索一个问题在**单个**文件中相关的内容。",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "请求的query,一般为一个问题,这个参数系统会自动填入用户的问题，不需要给出",
                    },
                    "fileid": {
                        "type": "string",
                        "description": "用户指定的文件的id",
                    },
                    "folder_id": {
                        "type": "string",
                        "description": "用户能访问的文件夹id列表，这个参数系统会自动填入，不需要给出",
                    },
                },
                "required": ["id"],
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "search_related_content",
            "description": "这是公司内部的存放标准文件的数据库的一个接口函数。这个函数通过传入关键字和用户的query，可以直接搜索用户输入的query与数据库中文件的相关内容。比如用户询问乳粉的霉菌标准是多少？你应当提取keywords：'乳粉 标准 霉菌 细菌 限制'.系统会自动填入query，folder_id_list等参数。函数会找到最相关的几个文本段落返回,并附带相关数量，来源，页数等。",
            "parameters": {
                "type": "object",
                "properties": {
                    "keywords": {
                        "type": "string",
                        "description": "你传入的关键字,一般为几个关键字。请你自动总结用户query中的关键字。比如用户query是： 1. 乳粉中大肠杆菌数量是多少？ 你应该总结：乳粉 细菌 限量 大肠杆菌",
                    },
                    "query": {
                        "type": "string",
                        "description": "用户的请求，这个参数系统会自动填入，不需要给出",
                    },
                    "folder_id_list": {
                        "type": "string",
                        "description": "用户能访问的文件夹id列表，这个参数系统会自动填入，不需要给出",
                    },
                },
                "required": ["keywords"],
            },
        }
    },
]



def searchSingleFile(query,id,folder_id_list):
    '''
    限定执行向量搜索. query是用户询问内容，id是文件id,folder_id_list是用户能访问的文件夹id列表。
    '''
    content,embeddings_model,embeddings_token=searchIndex(query,'sgsaf',idlist=[id],folder_id_list=folder_id_list)
    return content,embeddings_model,embeddings_token


def search_related_content(keywords:list[str],query:str,folder_id_list:list[str]=[]):
    '''
    根据用户输入的query和关键字，搜索数据库中与用户query相关内容。
    '''
    filename_list=searchIndex_filename(keywords,'sgsaf',folder_id_list=folder_id_list)
    
    contents=[]
    embeddings_tokens=0
    print('\n')
    logger.debug(f'开始逐个文件搜索,共{len(filename_list)}个文件,只搜索前5个文件\n')
    for i,filename in enumerate(filename_list):
        if i>5:break #只找前五的文件
        content,embeddings_model,embeddings_token=searchIndex(query,'sgsaf',idlist=[],folder_id_list=folder_id_list,filename_list=[filename])
        contents=contents+content
        embeddings_tokens=embeddings_tokens+embeddings_token
    
    return contents,embeddings_model,embeddings_tokens
    


def chat(query:str,context:list[dict],folder_id_list,corpus_id=''):
    '''
    chat 函数，调用gpt4o模型,返回聊天结果。实际上这个函数中可能是多次gpt使用工具的结果。
    return: 
        answer：即聊天回答
        references_contents: 引用的内容
        chat_model: 模型名称
        chat_tokens: 模型中token的个数
        embeddings_model: 模型名称
        embeddings_tokens: 模型中token的个数
    '''
    messages=[
                {'role':'system',
                 'content':'你是一个文档帮助机器人，你将为一家严格的标准检测组织工作，你拥有一个内部的存放标准文件的数据库的接口,请根据用户的问题判断是否需要查找数据库的相关文件等相关操作。请记住:1.只要有可能物品的标准判断产生关系，就要去数据库里找有没有相关文件 2.如果你没有执行查找数据库的操作，你要告诉用户你没有查找数据库而是根据自身知识 3.数据库中大多数是中文文件，如果用户提问为英文你要翻译成对应的中文'},
            ]
    messages+=context
    messages+=[{'role':'user','content':query}] if corpus_id=='-1' else [{'role':'user','content':query},{'role':'system','content':f'用户在本次提问中指定了文件id为{corpus_id}'}]

    references_contents=[]
    chat_tokens=0
    embedding_model='large'
    embedding_tokens=0

    while(True):
        response=openai.ChatCompletion.create(
            engine='AF-gpt4o', # The deployment name you chose when you deployed the GPT-35-turbo or GPT-4 model.
            messages=messages,
            tools=tools
        )
        messages.append(response.choices[0].message.to_dict_recursive())
        chat_tokens+=response.usage.total_tokens


        if 'tool_calls' in response.choices[0].message:
            logger.debug(f'gpt将调用函数')
            function_name=response.choices[0].message['tool_calls'][0]['function']['name']
            function_parameters=json.loads(response.choices[0].message['tool_calls'][0]['function']['arguments'])
            function_id=response.choices[0].message['tool_calls'][0]['id']
            function_call_info=f'''
            function_name         :{function_name}
            function_parameters   :{function_parameters}
            function_id           :{function_id}'''
            logger.debug(f'gpt调用函数信息：{function_call_info}')

            if function_name=='search_related_content':
                keywords=function_parameters['keywords']
                result,embedding_model,token=search_related_content(keywords,query,folder_id_list=folder_id_list)
                embedding_tokens+=token
                references_contents+=result
                tool_content=f"在语料数据库中搜索到{len(result)}条相关结果：{''.join([f'{num}. {i}   ' for num,i in enumerate(result,start=1)])},请你：1.首先判断用户提问的语言，使用对应的语言回答,用户的提问是：{query}。也不要说\'用户的语言是xx，因此我将用xx回答\'这种话 2.基于上面内容回答用户的问题,要求答案全面详细. 3.数据库搜索结果可能为空,也就是搜索到了0条语料，如果为空请告诉用户没有找到语料."
            
            if function_name=='searchSingleFile':
                fileid=function_parameters['fileid']
                result,embedding_model,token=searchSingleFile(query,fileid,folder_id_list)
                embedding_tokens+=token
                references_contents+=result
                tool_content=f"在语料数据库中搜索到{len(result)}条相关结果：{''.join([f'{num}. {i}   ' for num,i in enumerate(result,start=1)])},请你：1.首先判断用户提问的语言，使用对应的语言回答,用户的提问是：{query}。也不要说\'用户的语言是xx，因此我将用xx回答\'这种话 2.基于上面内容回答用户的问题,要求答案全面详细. 3.数据库搜索结果可能为空,也就是搜索到了0条语料，如果为空请告诉用户没有找到语料."

            messages.append({
                'role':'tool',
                'tool_call_id':function_id,
                'name' : function_name,
                'content':tool_content
            })

        else:
            logger.debug('gpt没有调用函数')
            answer=response.choices[0].message['content']
            chat_model=response.model
            chat_tokens=chat_tokens
            print(answer)
            return answer,references_contents,chat_model,chat_tokens,embedding_model,embedding_tokens




def parse_context(context:str):
    '''
    传入的context为字符串, 格式为：
    USER QUESTION: XXXX
    ASSISTANT ANSWER: XXX
    USER QUESTION: XXX
    ASSISTANT ANSWER: XXX
    ....
    要解析成字典列表格式，也就是：
    [{'role': 'user', 'contenxt': 'XXXX'},
     {'role': 'assistant', 'contenxt': 'XXX'},
     {'role': 'user', 'contenxt': 'XXX'},
     {'role': 'assistant', 'contenxt': 'XXX'}]
    '''
     # 正则表达式匹配模式
    pattern = re.compile(r'(USER QUESTION:|ASSISTANT ANSWER:)\s*(.+?)(?=\nUSER QUESTION:|\nASSISTANT ANSWER:|$)', re.DOTALL)
    
    matches = pattern.findall(context)
    
    # 构造字典列表
    conversation = []
    for match in matches:
        role = 'user' if match[0] == 'USER QUESTION:' else 'assistant'
        content = match[1] if role == 'user' else match[1][1:]
        conversation.append({'role': role, 'content': content})
    
    return conversation


def receive(data: dict):
    """
    :param data:
    :return:
    """
    logger.debug(f'recieved data:{data}')
    folder_id_list=data['folder-ids'].split(',')
    corpus_id=data['corpus-id']

    context=parse_context(data['context'])
    newques = data['question']
    '''
        response_content：即聊天回答
        references_contents: 引用的内容
        chat_model: 模型名称
        chat_tokens: 模型中token的个数
        embeddings_model: 模型名称
        embeddings_tokens: 模型中token的个数
        '''
    
    answer,references_contents,chat_model,chat_tokens,embeddings_model,embeddings_tokens=chat(newques,context,folder_id_list,corpus_id)


    '''
    接下来要处理：
    1.格式化references
    2.格式化模型，token计算
    '''

    s = ''  # s for source
    for index in range(len(references_contents)):
        if index != len(references_contents) - 1:
            s = s + references_contents[index]['tag'] + ' file_id:' + references_contents[index]['idfile'] + '、'
        else:
            s = s + references_contents[index]['tag'] + ' file_id:' + references_contents[index]['idfile']+ '。'
    b='/model:' + str(chat_model) + '/tokens:' + str(chat_tokens) + '/embeddings_model:' + str(embeddings_model) + '/embeddings_tokens:' + str(embeddings_tokens)
    interval = "INTERVAL"

    if answer[:3] == '对不起' or answer[:3] == '很抱歉':
         a = answer + '\n\n'
         # return answer + '\n\n' + '/model:' + str(model) + '/tokens:' + str(tokens) + '/embeddings_model:' + str(embeddings_model) + '/embeddings_tokens:' + str(embeddings_tokens)
         return a + interval + b
    else:
         a = answer + '\n\n' + s
         # return answer + '\n\n' + s + '/model:' + str(model) + '/tokens:' + str(tokens) + '/embeddings_model:' + str(embeddings_model) + '/embeddings_tokens:' + str(embeddings_tokens) + '\n\n'
         return a + interval + b



data={
    'corpus-id': '17964',
    'question': '食品容器包装的标准有什么要求',
    'folder-ids':'测试语料,test1,标准库,ADD',
    'context': '''USER QUESTION:记住我叫jack
ASSISTANT ANSWER:"好的
USER QUESTION:酸奶样品，测试脂肪，GB5009.5-2016第二法和第三法哪个比较适用
ASSISTANT ANSWER:"对不起，没有在语料中找到相关的文件
USER QUESTION:酸奶样品，测试脂肪，GB5009.5-2016第二法和第三法哪个比较适用
ASSISTANT ANSWER:"对不起，没有在语料中找到相关的文件'''
}

print(receive(data))
