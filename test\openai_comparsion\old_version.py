import sys
sys.path.append('/home/<USER>/jack/chatbot-alg/chatcode')
for i in sys.path:print(i)
from fastapi import FastAPI
from askdoc.generation.azure_openai_generation import AzureOpenAIGenerationModule

app = FastAPI()


generation_module = AzureOpenAIGenerationModule(
    api_key='doesnotmatter',
    api_base="https://app-52ezhysttga2w.jollypebble-72e46cd0.southeastasia.azurecontainerapps.io",
    api_version='2024-02-01',
)


@app.post("/chat")
def chat(data:dict):
    answer = generation_module.generate(
        engine='AF-gpt4o',
        messages=[
            {"role": "user", "content": data['prompt']}
        ],
    )
    return answer

if __name__ == '__main__':
    import uvicorn
    uvicorn.run('old_version:app', host='0.0.0.0', port=8000,workers=3)