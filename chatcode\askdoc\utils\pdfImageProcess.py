import sys
sys.path.append('/mnt/datadisk1/deployment/chatbot')
import io
import logging
logger_cmd=logging.getLogger(__name__)
logger_cmd.setLevel(logging.DEBUG)
import multiprocessing
import traceback
from logging.handlers import RotatingFileHandler

import fitz
from paddleocr import PaddleOCR
from PIL import Image

from searchimg.pdfPdf2 import generate_random_string
from askdoc.utils.commonUtils import identify_file_type
import pdfplumber
import tiktoken


# 创建进程池
def extract_tables_from_pdf(pdf_file_path):
    tables_data = []

    with pdfplumber.open(pdf_file_path) as pdf:
        for page in pdf.pages:
            page_tables = []
            for table in page.extract_tables():
                table_data = []
                for row in table:
                    row_data = [cell.strip().replace('\n', '').replace(' ', '') if cell is not None else "" for cell in row]
                    table_data.append(row_data)
                page_tables.append(table_data)
            tables_data.append(page_tables)

    return tables_data
def log_to_file(process_id):
    # 配置日志记录器
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    # 设置日志格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(message)s')
    # 创建RotatingFileHandler，指定日志文件名、最大文件大小、和备份文件的个数
    log_handler = RotatingFileHandler(f'process_{process_id}.log', maxBytes=10240, backupCount=3)
    log_handler.setFormatter(formatter)
    # 将处理程序添加到记录器
    logger.addHandler(log_handler)
    return logger
def log_to_file(process_id):
    # 配置日志记录器
    logging.basicConfig(filename=f'process_{process_id}.log', level=logging.INFO, format='%(asctime)s - %(name)s - %(message)s')
    logger = logging.getLogger(__name__)
    return  logger



def num_tokens_from_messages(messages):
    encoding = tiktoken.get_encoding("cl100k_base")
    num_tokens = 0
    for message in messages:
        if not isinstance(message, dict):
            print(f"Warning: Expected dict, got {type(message)}. Message: {message}")
            continue

        num_tokens += 4  # 每条消息的开头和结尾
        for key, value in message.items():
            num_tokens += len(encoding.encode(value))
            if key == "name":
                num_tokens -= 1  # 如果有name，减去role的token计数

    num_tokens += 2  # 每个回复以<im_start>assistant开始
    return num_tokens


def extract_pdf_page(text_for_nextpage, core_range, tag, file_bytes, max_tokens=1024,filename='1',ct='0',et='0',fileid='1',folder_id='test1'):
    logger_cmd.debug('==========================进程开始执行============================')
    # print(core_range)
    ocr = PaddleOCR(use_angle_cls=False,show_log=False)
    try:
        logger = log_to_file(str(core_range[0]) + "_" + str(core_range[1]))
        logger.info('开始.....')
        with fitz.open(stream=io.BytesIO(file_bytes)) as pdf_document, pdfplumber.open(io.BytesIO(file_bytes),) as table_pdf:
            pages_result = []
            remaining_text = ''
            update_text_for_nextpage = ''
            for page_number in range(core_range[0], core_range[1] + 1):
                logger.debug(f"core_range[0]: {str(core_range[0])} core_range[1]: {str(core_range[1]+1)}")
                page_number = page_number - 1
                try:
                    logger.debug(f"Start of loop, text_for_nextpage: {text_for_nextpage}")
                    logger.info(f"开始处理页面 {page_number}")
                    page = pdf_document[page_number]
                    logger.debug(f"Processing Page {str(page_number)}")
                    page = pdf_document[page_number]
                    try:
                        page_tables = []
                        table_page = table_pdf.pages[page_number]

                        # 提取表格文本
                        for table in table_page.extract_tables():
                            for row in table:
                                row_text = ' '.join([cell for cell in row if cell])
                                page_tables.append(row_text)
                    except:
                        logger.error(f"提取表格出现问题，跳过")

                    # 提取文本
                    #page_text = remaining_text + page.get_text().strip() + ' '.join(page_tables)
                    page_text = text_for_nextpage + page.get_text().strip() + ' '.join(page_tables)
                    remaining_text = ''

                    # 处理图像和OCR
                    images = page.get_images(full=True)
                    image_text_list = []
                    if images:
                        for img_index, image in enumerate(images):
                            xref = image[0]
                            base_image = pdf_document.extract_image(xref)
                            image_bytes_io = io.BytesIO(base_image["image"])
                            ocr_results = ocr.ocr(image_bytes_io.read())
                            #print("111111111111111111111111111111111111111111111111111111111")
                            logger_cmd.debug(f'ocr_results:{ocr_results}')
                            #print("111111111111111111111111111111111111111111111111111111111")
                            if ocr_results:
                                # 检查ocr_results是否为列表的列表
                                if all(isinstance(item, list) for item in ocr_results):
                                    # 如果是列表的列表，使用嵌套循环处理
                                    for detections in ocr_results:
                                        for detection in detections:
                                            if detection is None:
                                                continue  # 跳过这个None对象，继续处理下一个元素
                                            bbox, text_confidence_tuple = detection
                                            text, confidence = text_confidence_tuple  # Unpack text and confidence
                                            image_text_list.append(text)  # Add text to the list
                                else:
                                    # 如果不是列表的列表，使用单个循环处理
                                    for detection in ocr_results:
                                        if detection is None:
                                            continue  # 跳过这个None对象，继续处理下一个元素
                                        bbox, text_confidence_tuple = detection
                                        text, confidence = text_confidence_tuple  # Unpack text and confidence
                                        image_text_list.append(text)  # Add text to the list

                    # 纯图片页面
                    # 如果整个页面都是图片，没有文本和表格
                    if not images and not table_page.extract_tables() and not page.get_text():
                        logger.info(f'{str(page_number)} 转化为图片识别')
                        pix = page.get_pixmap()
                        pil_image = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                        image_bytes = io.BytesIO()
                        pil_image.save(image_bytes, format="PNG")
                        image_bytes = image_bytes.getvalue()
                        result = ocr.ocr(image_bytes)
                        logger_cmd.debug(f'result:{result}')
                        if result:
                            for line in result:
                                text = line[1][0]  # 同样确保从结果中提取文本
                                image_text_list.append(text)

                    logger_cmd.debug("*******************************************************************")
                    logger_cmd.debug(f"image_text_list:{image_text_list}")
                    logger_cmd.debug("*******************************************************************")
                    # page_text += ' '.join(filter(lambda item: isinstance(item, str), image_text_list))
                    page_text += ' '.join(image_text_list)  # 确保image_text_list只包含字符串


                    # 按照 tokens 分割文本
                    temp_text = ""
                    for char in page_text:
                        temp_text += char
                        if num_tokens_from_messages([{"content": temp_text}]) > max_tokens:
                            pages_result.append({'id': generate_random_string(), 'content': temp_text[:-1], 'tag': "来源:" + str(filename) + " 第" + str(page_number + 1) + "页",'filename':filename ,"starttime":ct,"endtime":et,'idfile':fileid,'folder_id':folder_id, 'filetype': identify_file_type(filename)})
                            temp_text = char
                    remaining_text = temp_text
                    if remaining_text:
                        update_text_for_nextpage = remaining_text
                        pages_result.append({'id': generate_random_string(), 'content': remaining_text, 'tag': "来源:" + str(filename) + " 第" + str(page_number + 1) + "页",'filename':filename,"starttime":ct,"endtime":et,'idfile':fileid,'folder_id':folder_id, 'filetype': identify_file_type(filename)})
                    else:
                        update_text_for_nextpage = ''

                    logger.debug(f"End of loop, text_for_nextpage: {text_for_nextpage}")
                    logger.info(f"页面 {page_number} 处理完成")
                    logger_cmd.debug(f'页面 {page_number} 处理完成')
                except Exception as e:
                    logger.error(f"处理页面 {page_number} 时发生错误: {e}")
                    logger_cmd.error(f"处理页面 {page_number} 时发生错误: {e}")
                    traceback.print_exc()

            logger.info('-----------------------------------------------返回------------')
            return pages_result,update_text_for_nextpage
    except Exception as e:
        traceback.print_exc()
        print("Error processing page", str(e))


def extract_pdf_content_and_images(pdf_file_path, tag="", max_tokens=1024,filename='1',ct='0',et='0',fileid='1',folder_id='test1'):
    with open(pdf_file_path, 'rb') as file_stream:
        file_bytes = file_stream.read()

    pdf_document = fitz.open(stream=io.BytesIO(file_bytes))

    total_pages = len(pdf_document)
    merged_results = []

    text_for_nextpage = ''
    # 直接在单个进程中处理每一页
    for page_number in range(1, total_pages + 1):
        core_range = [page_number, page_number]
        page_results,text_for_nextpage = extract_pdf_page(text_for_nextpage,core_range, tag, file_bytes, max_tokens,filename,ct,et,fileid,folder_id)
        if page_results:
            merged_results.extend(page_results)

    # 关闭 PDF 文档
    pdf_document.close()

    print("Results:", len(merged_results))
    return merged_results
        

def allocate_tasks(total_pages, num_cores):
    # 计算每个核心需要处理的页数
    pages_per_core = total_pages // num_cores

    # 创建一个字典来存储每个核心的任务范围
    task_ranges = {}

    # 分配任务给每个核心
    start_page = 1
    for core_id in range(num_cores):
        if core_id == num_cores - 1:
            # 最后一个核心处理剩余的所有页
            end_page = total_pages
        else:
            end_page = start_page + pages_per_core - 1

        # 存储任务范围
        task_ranges[core_id] = (start_page, end_page)

        # 更新下一个核心的起始页
        start_page = end_page + 1

    return task_ranges
if __name__ == '__main__':
    # 指定PDF文件路径
    pdf_file_path = r'D:\资料\工作资料\ChatGPT\代码\chatcode\Retrieval-Augmented Generation for Large Language Models：A Survey.pdf'  # 替换为实际PDF文件路径

    with open(pdf_file_path, 'rb') as file_stream:
        results = extract_pdf_content_and_images(file_stream, tag="示例标签")
        for result in results:
            print(result)



