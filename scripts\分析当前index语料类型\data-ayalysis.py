'''统计datafile中文件类型占比'''
import pandas as pd
import matplotlib.pyplot as plt
import os
import numpy as np

# 设置字体以支持中文
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK JP']  # 使用Noto Sans CJK字体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

# 假设你的文件内容存储在一个文本文件中
file_path = 'datafile'

# 读取文件内容
with open(file_path, 'r', encoding='utf-8') as file:
    lines = file.readlines()

# 解析文件内容
data = []
for line in lines:
    parts = line.strip().split(' / ')
    if len(parts) == 3:
        id, directory, filename = parts
    else:
        id, directory = parts[0], parts[1]
        filename = None
    data.append([id, directory, filename])

# 创建DataFrame
df = pd.DataFrame(data, columns=['ID', 'Directory', 'Filename'])

# 分析ID数量
unique_ids = df['ID'].nunique()

# 分析目录种类数量
unique_directories = df['Directory'].nunique()

# 分析文件类型占比
def get_filetype(filename):
    if pd.isnull(filename):
        return '问答对'
    ext = os.path.splitext(filename)[1].lower()
    if ext == '':
        return '问答对'
    return ext

df['Filetype'] = df['Filename'].apply(get_filetype)
filetype_counts = df['Filetype'].value_counts(normalize=True)

# 合并 '.pdf' 和 '.PDF'
if '.PDF' in filetype_counts:
    filetype_counts['.pdf'] += filetype_counts['.PDF']
    filetype_counts = filetype_counts.drop('.PDF')

# 打印结果
print(f"共有 {unique_ids} 个文件（ID）。")
print(f"共有 {unique_directories} 个目录种类。")
print("文件类型占比：")
print(filetype_counts)

# 可视化文件类型占比
# 显示前8个类型，其余的合并为“其他”
top_filetypes = filetype_counts.head(8)
other_filetypes_proportion = filetype_counts[8:].sum()
filetype_counts_top8 = pd.concat([top_filetypes, pd.Series(other_filetypes_proportion, index=['其他'])])

# 设置浅蓝色
colors = ['#87CEEB'] * len(filetype_counts_top8)

# 绘制条形图
fig, ax = plt.subplots(figsize=(14, 10))
plt.grid(True)
bars = ax.bar(filetype_counts_top8.index, filetype_counts_top8, color=colors, edgecolor='black', alpha=1)

# 美化条形图
for bar in bars:
    bar.set_linewidth(1)
    bar.set_edgecolor('black')

ax.set_title('文件类型统计', fontsize=20)
ax.set_xlabel('文件类型', fontsize=16)
ax.set_ylabel('占比', fontsize=16)
ax.tick_params(axis='x', labelsize=14)
ax.tick_params(axis='y', labelsize=14)

# 添加细节
for i, (count, label) in enumerate(zip(filetype_counts_top8, filetype_counts_top8.index)):
    ax.text(i, count + 0.001, f'{count:.1%}', ha='center', va='bottom', fontsize=14)

plt.text(0.70, 0.80, f'共有 {unique_ids} 个文件（ID）', fontsize=16, transform=plt.gcf().transFigure)
plt.text(0.70, 0.75, f'共有 {unique_directories} 个目录种类', fontsize=16, transform=plt.gcf().transFigure)

plt.xticks(rotation=45)
plt.show()
