import openai
from typing import Optional,Union,List,Dict
from .generation import BaseGenerationModule


class AzureOpenAIGenerationModule(BaseGenerationModule):
    def __init__(
            self,
            api_key: str,
            api_base:str,
            api_version:str,
    ):
        self.api_type = "azure"
        self.api_key = api_key
        self.api_base = api_base
        self.api_version = api_version
    
    def generate(
            self,
            engine:str,
            messages:Optional[List[Dict]] = None,
            tools:Optional[List[Dict]] = None,
            **kwargs,
            ) -> Dict:
        
        openai.api_type = self.api_type
        openai.api_key = self.api_key
        openai.api_base = self.api_base
        openai.api_version = self.api_version

        response = openai.ChatCompletion.create(
            engine=engine,
            messages=messages,
            tools=tools,
            **kwargs,
        )

        return response