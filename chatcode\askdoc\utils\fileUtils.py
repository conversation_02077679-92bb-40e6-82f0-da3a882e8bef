#!/usr/bin/python3
# -*- coding: utf-8 -*-

import os
import time
import requests
from lxml import etree


def get_range(_lst):
    """
    :param _lst:
    :return:
    """
    num_list = list(map(lambda d: int(d), _lst))
    grouped_list = []
    group = []
    for i in range(len(num_list)):
        # 如果当前数字与前一个数字连续
        if i > 0 and num_list[i] == num_list[i - 1] + 1:
            group.append(num_list[i])
        # 不连续时，将已形成的分组添加到grouped_list中
        else:
            if group:
                grouped_list.append(group)
            # 创建新的分组
            group = [num_list[i]]
    # 添加最后一个分组
    if group:
        grouped_list.append(group)

    range_list = []
    for group in grouped_list:
        if len(group) > 1:
            range_list.append(f"{group[0]}~{group[-1]}")
        else:
            range_list.append(str(group[0]))
    return range_list


def print_log(*args):
    """
    打印日志
    :param args:
    :return:
    """
    print('*' * 30 + time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()) + '*' * 30)
    for arg in args:
        print(arg)


def download_file(url, params, local_filename):
    """
    下载文件
    :param url: 
    :param params: 
    :param local_filename: 
    :return: 
    """
    response = requests.get(url, params=params, stream=True)
    # 确保目录存在
    os.makedirs(os.path.dirname(local_filename), exist_ok=True)
    if response.status_code == 200:
        with open(local_filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=1024):
                f.write(chunk)
    else:
        print_log(f"HTTP请求失败，状态码：{response.status_code}")


def parse_xml_and_download(xml_content, download_url, download_params, download_directory):
    """
    下载文件并解析xml
    :param xml_content: 
    :param download_url: 
    :param download_params: 
    :param download_directory: 
    :return: 
    """
    root = etree.fromstring(xml_content)
    print_log(download_directory, download_url, download_params, root)

    files_info = []
    for page_data in root.xpath('.//pageData'):
        file_info = {
            'CREATE_TIME': page_data.findtext('CREATE_TIME'),
            'EXPIRE_TIME': page_data.findtext('EXPIRE_TIME', default=''),
            'ID': page_data.findtext('ID'),
            'NAME': page_data.findtext('NAME')
        }

        download_params['FILE_ID'] = file_info['ID']
        download_file_url = download_url
        download_file_params = download_params

        if file_info['NAME']:
            local_filename = os.path.join(os.path.dirname(os.path.dirname(__file__)), download_directory, file_info['NAME'])

            # 确保目录存在
            os.makedirs(os.path.dirname(local_filename), exist_ok=True)

            # 下载文件
            download_file(download_file_url, download_file_params, local_filename)

            file_info['FILE_PATH'] = local_filename
            files_info.append(file_info)

    return files_info
