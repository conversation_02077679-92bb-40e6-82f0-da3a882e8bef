import logging
from tavily import Tavily<PERSON><PERSON>
from typing import Dict
from .websearch import BaseWebSearchModule

# 设置日志
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter('[%(asctime)s] [%(levelname)s] %(filename)s:%(lineno)d - %(message)s'))
logger.addHandler(handler)
logger.setLevel(logging.INFO)

class TavilyWebSearchModule(BaseWebSearchModule):
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.tavily_client = TavilyClient(api_key=api_key)
    
    def websearch(self, query: str) -> Dict:
        """
        使用 TavilyClient 进行 Web 搜索，并记录搜索日志和结果数量。
        
        参数:
            query (str): 搜索查询字符串
        
        返回:
            Dict: 搜索结果字典
        """
        logger.info(f'正在搜索: {query}')
        response = self.tavily_client.search(query=query)
        logger.info(f'搜索结果: {len(response["results"])}')
        return response
