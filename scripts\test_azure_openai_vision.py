"""
这是一个测试脚本，用于测试Azure OpenAI的GPT4O模型ocr性能
"""

from openai import AzureOpenAI

api_base = "https://af-eastus-aoai.openai.azure.com/"
api_key = "********************************"
deployment_name = "AF-gpt4o"
api_version = "2023-12-01-preview"  # this might change in the future

client = AzureOpenAI(
    api_key=api_key,
    api_version=api_version,
    base_url=f"{api_base}/openai/deployments/{deployment_name}",
)


import base64
from mimetypes import guess_type


# Function to encode a local image into data URL
def local_image_to_data_url(image_path):
    # Guess the MIME type of the image based on the file extension
    mime_type, _ = guess_type(image_path)
    if mime_type is None:
        mime_type = "application/octet-stream"  # Default MIME type if none is found

    # Read and encode the image file
    with open(image_path, "rb") as image_file:
        base64_encoded_data = base64.b64encode(image_file.read()).decode("utf-8")

    # Construct the data URL
    return f"data:{mime_type};base64,{base64_encoded_data}"


# Example usage
image_path = "/home/<USER>/jack/chatbot-alg/scripts/testimg3.png"
data_url = local_image_to_data_url(image_path)
print("Data URL:", data_url)


response = client.chat.completions.create(
    model=deployment_name,
    messages=[
        {"role": "user", "content": "You are a helpful assistant."},
        {
            "role": "user",
            "content": [
                {"type": "text", "text": "描述表格内容，并尝试以html形式复原表格"},
                {"type": "image_url", "image_url": {"url": f"{data_url}"}},
            ],
        },
    ],
    max_tokens=2000,
)

print(response.choices[0].message.content)
