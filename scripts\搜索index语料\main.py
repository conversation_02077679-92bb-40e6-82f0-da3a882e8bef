import sys
import os
import json

# 获取当前脚本的目录
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取 chatcode 模块的绝对路径
module_path = os.path.abspath(os.path.join(current_dir, "../../"))
sys.path.append(module_path)
config_path = os.path.join(
    os.path.dirname(__file__), "../../chatcode/askdoc/config.json"
)
with open(config_path, "r", encoding="utf-8") as f:
    config = json.load(f)
config_search = {
    "endpoint": config["main_rag_pipeline_config"]["search_api_endpoint"],
    "key": config["main_rag_pipeline_config"]["search_api_key"],
    "index_name": config["main_rag_pipeline_config"]["search_api_index_name"],
}
# print(f"using config:", json.dumps(config_search, indent=2, ensure_ascii=False))

from chatcode.askdoc.retrival.azure_aisearch_retrival import AzureAISearchRetrivalModule

if __name__ == "__main__":
    retrival_module = AzureAISearchRetrivalModule(**config_search)

    filter = {"idfile": ["13801"]}
    result = retrival_module.search(
        filter=retrival_module.filter_list2filter(filter),
        select=["idfile", "filename", "content"],
        search_text_or_list="*",
    )

    for i, res in enumerate(result):
        print(json.dumps(res, ensure_ascii=False, indent=4))
        # print(i['filename'],'\n',i['content'],)
    # print(result[0]["keywords_search_results"].__len__())
