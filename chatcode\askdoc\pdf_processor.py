# pdf_processor.py
import logging
logger=logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
import sys
import os
sys.path.append(os.path.dirname(__file__)+"/..")
import time
import traceback
import time
import traceback
import datetime
#from askdoc.utils.ask import chat, chatWithLimit
from askdoc.utils.indexCreate import createIndex, delIndex
from askdoc.utils.pdfSource import reader_pdf
from askdoc.utils.pdfSource import read_excel,read_excel2markdown
from askdoc.utils.pdfSource import read_images
from askdoc.utils.pdfSource import read_ppt
from askdoc.utils.pdfSource import read_csv
from askdoc.utils.pdfSource import read_txt
from askdoc.utils.pdfSource import read_qa
from askdoc.utils.pdfSource import convert_to_pdf
from askdoc.utils.uploadData import fillDataToIndex
from askdoc.utils.vectorCreate import createVector
from fastapi.middleware.cors import CORSMiddleware
from docx2pdf import convert
# from database_ops import update_corpus_status


def process_pdf(pdf_file_path, filename='1', ct='0', et='0', fileid='1',folder_id='test1', indexName='111'):
    indexName = 'sgsaf'
    # update_corpus_status(ID = indexName,EMBEDDING_STATUS=2)
    start_time = time.time()
    document=reader_pdf(pdf_file_path=pdf_file_path,filename=filename,ct=ct,et=et,fileid=fileid,folder_id=folder_id)
    createVector(document)
    createIndex(indexName)
    fillDataToIndex(document,indexName)
    end_time = time.time()
    elapsed_time = end_time - start_time
    logger.debug(f"用时:{elapsed_time} 秒")
    return document

def process_excel_pdf(pdf_file_path, filename='1', ct='0', et='0', fileid='1', indexName='111',folder_id='test1'):
    indexName = 'sgsaf'
    try:
        start_time = time.time()
        document=read_excel2markdown(file_path=pdf_file_path,filename=filename,ct=ct,et=et,fileid=fileid,folder_id=folder_id)
        #document=read_excel(file_path=pdf_file_path,filename=filename,ct=ct,et=et,fileid=fileid,folder_id=folder_id)
        print(document)
        createVector(document)
        createIndex(indexName)
        # input(f'fileid:{fileid} process completed,input to continue')
        try:
            fillDataToIndex(document,indexName)
            with open('embedded_fileid.txt','a') as f:
                f.write(fileid+'\n')
        except Exception as e:
            print('异常退出', e)
            traceback.print_exc()
        end_time = time.time()
        elapsed_time = end_time - start_time
        print(f"用时:{elapsed_time} 秒")
        return document
    except Exception as e:
        print('异常退出', e)
        traceback.print_exc()

def process_qa(qa=' ',filename='1', ct='0', et='0', fileid='1', indexName='111',folder_id='test1'):
    indexName = 'sgsaf'
    try:
        start_time = time.time()
        document=read_qa(qa=qa,filename=filename,ct=ct,et=et,fileid=fileid,folder_id=folder_id)
        print(document)
        createVector(document)
        createIndex(indexName)
        fillDataToIndex(document,indexName)
        end_time = time.time()
        elapsed_time = end_time - start_time
        print(f"用时:{elapsed_time} 秒")
        return document
    except Exception as e:
        print('异常退出', e)
        traceback.print_exc()


def process_csv_pdf(pdf_file_path, filename='1', ct='0', et='0', fileid='1', indexName='111',folder_id='test1'):
    indexName = 'sgsaf'
    try:
        start_time = time.time()
        document=read_csv(file_path=pdf_file_path,filename=filename,ct=ct,et=et,fileid=fileid,folder_id=folder_id)
        print(document)
        createVector(document)
        createIndex(indexName)
        fillDataToIndex(document,indexName)
        end_time = time.time()
        elapsed_time = end_time - start_time
        print(f"用时:{elapsed_time} 秒")
        return document
    except Exception as e:
        print('异常退出', e)
        traceback.print_exc()



def process_txt_pdf(pdf_file_path,filename='1', ct='0', et='0', fileid='1', indexName='111',folder_id='test1'):
    indexName = 'sgsaf'
    try:
        start_time = time.time()
        document=read_txt(file_path=pdf_file_path,filename=filename,ct=ct,et=et,fileid=fileid,folder_id=folder_id)
        print(document)
        createVector(document)
        createIndex(indexName)
        fillDataToIndex(document,indexName)
        end_time = time.time()
        elapsed_time = end_time - start_time
        print(f"用时:{elapsed_time} 秒")
        return document
    except Exception as e:
        print('异常退出', e)
        traceback.print_exc()

def process_ppt_pdf(pdf_file_path, filename='1', ct='0', et='0', fileid='1', indexName='111',folder_id='test1'):
    indexName = 'sgsaf'
    try:
        start_time = time.time()
        document=read_ppt(ppt_file=pdf_file_path,filename=filename,ct=ct,et=et,fileid=fileid,folder_id=folder_id)
        print(document)
        createVector(document)
        createIndex(indexName)
        fillDataToIndex(document,indexName)
        end_time = time.time()
        elapsed_time = end_time - start_time
        print(f"用时:{elapsed_time} 秒")
        return document
    except Exception as e:
        print('异常退出', e)
        traceback.print_exc()

def process_image_pdf(pdf_file_path,filename='1', ct='0', et='0', fileid='1', indexName='111',folder_id='test1'):
    print("------------")
    indexName='sgsaf'
    
    start_time = time.time()
    document=read_images(file_path=pdf_file_path,filename=filename,ct=ct,et=et,fileid=fileid,folder_id=folder_id)
    print(document)
    createVector(document)
    createIndex(indexName)
    fillDataToIndex(document,indexName)
    end_time = time.time()
    elapsed_time = end_time - start_time
    print(f"用时:{elapsed_time} 秒")
    return document
    

if __name__=='__main__':
    # pdf_file_path = '/home/<USER>/jack/newnewjack/chatcode/uploads/Excel格式语料测试.xlsx'
    # process_excel_pdf(pdf_file_path=pdf_file_path)
    # 处理路径下的excel文件
    # excel_file_path = '/home/<USER>/jack/newnewjack/chatcode/uploads/北京客户保健食品稳定性试验报告，客户还需提供CMA资料有什么要求？.xlsx'
    # process_excel_pdf(pdf_file_path=excel_file_path)
    # pdf_file_path = '/home/<USER>/jack/newnewjack/chatcode/uploads/计算机网络授课ppt.pptx'
    # process_ppt_pdf(pdf_file_path=pdf_file_path,filename='计算机网络授课ppt')
    # picture_path='/home/<USER>/jack/chatbot-alg/testfile/screenshot-knowrob2.0 A 2nd Generation Knowledge Processing Framework.jpg'
    # process_image_pdf(pdf_file_path=picture_path,filename='screenshot-knowrob2.0 A 2nd Generation Knowledge Processing Framework.jpg')
    # text_path='/home/<USER>/jack/chatbot-alg/testfile/16位的操作系统比32位快得多的原因.txt'
    # process_txt_pdf(text_path,filename='16位的操作系统比32位快得多的原因.txt')
    # csv_path='/home/<USER>/jack/chatbot-alg/testfile/测试文件 甘特项目规划器.csv'
    # process_csv_pdf(csv_path,filename='测试文件 甘特项目规划器.csv')
    pass