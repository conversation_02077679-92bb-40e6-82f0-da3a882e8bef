"""
测试SearchSingleCorpusPipeline

"""

import sys
sys.path.append('./chatcode')
import json

from askdoc.pipelines.search_corpus_pipeline import SearchSingleCorpusPipeline


def main():
    pipeline = SearchSingleCorpusPipeline(
        azure_openai_api_key="********************************",
        azure_openai_api_base="https://af-eastus-aoai.openai.azure.com/",
        azure_openai_api_version="2024-02-01",
        search_api_endpoint="https://afaisearch.search.windows.net",
        search_api_key="jXlotzlexllX10tW5cfxBlazrKrprNnDKS4xAy3ypYAzSeD0lqiS",
        search_api_index_name="sgsaf"
    )

    filter = {
        'idfile':['13480']
    }

    """
    filter参数要注意, idfile不要当字符串：
    filter:
    idfile: Option[List] = None
    """

    print(json.dumps(pipeline.run(["皮革水解物添加的危害是什么"], filter),ensure_ascii=False,indent=2))
    print(pipeline.filter_string)

if __name__ == "__main__":
    main()