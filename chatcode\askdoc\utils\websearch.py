from tavily import TavilyClient
import logging
logger=logging.getLogger(__name__)
handler=logging.StreamHandler()
handler.setFormatter(logging.Formatter('[%(asctime)s] [%(levelname)s] %(filename)s:%(lineno)d - %(message)s'))
logger.addHandler(handler)
logger.setLevel(logging.INFO)
api_key='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'


# tavily = TavilyClient(api_key=api-key)
# # 基本搜索：
# response = tavily.search(query="Should I invest in Apple in 2024?")
# # print('For basic search: \n',response)
# # print(response['results'])
# [print(i) for i in response['results']]

def websearch(query:str):
    tavily = TavilyClient(api_key=api_key)
    logger.info(f'正在搜索: {query}')
    response = tavily.search(query=query)
    logger.info(f'搜索结果: {len(response["results"])}')
    return response