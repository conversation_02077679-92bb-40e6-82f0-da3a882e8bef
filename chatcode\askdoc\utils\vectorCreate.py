# Import required libraries
import os
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
handler = logging.StreamHandler()
formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
handler.setFormatter(formatter)
logger.addHandler(handler)
handler.setLevel(logging.DEBUG)
import json
import openai
from tenacity import retry, wait_random_exponential, stop_after_attempt
from azure.core.credentials import AzureKeyCredential
from database_ops import update_embedding_token_count

CONFIG_DIR = os.path.join(os.path.dirname(__file__), "../config.json")
with open(CONFIG_DIR, "r") as f:
    config = json.load(f)

pipeline_config = config.get("main_rag_pipeline_config")
service_endpoint = pipeline_config.get("search_api_endpoint")
azure_key = pipeline_config.get("search_api_key")

openai.api_type = "azure"
embedding_config = config.get('other_config',{}).get('embedding_config',{})
openai.api_base=embedding_config.get('azure_openai_api_base')
openai.api_key=embedding_config.get('azure_openai_api_key')
openai.api_version=embedding_config.get('azure_openai_api_version')

credential = AzureKeyCredential(azure_key)


@retry(wait=wait_random_exponential(min=1, max=20), stop=stop_after_attempt(6))
# Function to generate embeddings for title and content fields, also used for query embeddings
def generate_embeddings(text):
    logger.debug(
        f"api_type: {openai.api_type}, api_base: {openai.api_base}, api_version: {openai.api_version}, api_key: {openai.api_key}"
    )
    response = openai.Embedding.create(input=text, engine="large")
    embeddings = response["data"][0]["embedding"]
    embeddings_tokens = response["usage"]["total_tokens"]
    embeddings_model = response["model"]
    return embeddings, embeddings_model, embeddings_tokens


# Generate Document Embeddings using OpenAI Ada 002
def createVector(inputfile):
    # Read the text-sample.json
    with open("data/jsonfile_{}.json".format(inputfile), "r", encoding="utf-8") as file:
        input_data = json.load(file)

    # Generate embeddings for title and content fields
    total_embedding_token = 0
    for item in input_data:
        # title = item['title']
        content = item["content"]
        # title_embeddings = generate_embeddings(title)
        content_embeddings, _, embedding_tokens = generate_embeddings(content)
        total_embedding_token += embedding_tokens

        # item['titleVector'] = title_embeddings
        item["contentVector"] = content_embeddings

    # Output embeddings to docVectors.json file
    with open("data/jsonvector_{}.json".format(inputfile), "w") as f:
        json.dump(input_data, f)

    file_id = item["idfile"]
    logger.debug(f"document {file_id} consumes {total_embedding_token} tokens")
    # update_embedding_token_count(ID=file_id,embedding_token=total_embedding_token)


if __name__ == "__main__":
    aa = "data/{}.json".format("cccc")
    print(aa)
