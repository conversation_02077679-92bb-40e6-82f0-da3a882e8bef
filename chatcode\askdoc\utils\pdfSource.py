import os
import logging

from askdoc.utils import excel2markdown
logger=logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
import sys
sys.path.append(os.path.dirname(os.getcwd())+'/..')
import multiprocessing
import random
import string
import traceback
import io
import csv
import json
import PyPDF2
import sys

import pandas as pd
from PIL import Image
from pptx import Presentation
from pptx.enum.shapes import MSO_SHAPE_TYPE
from openpyxl import load_workbook

from azure.core.credentials import AzureKeyCredential
from azure.search.documents import SearchClient

from concurrent import futures as cf
sys.path.append('/mnt/datadisk1/deployment/chatbot')
from askdoc.config import *
import tiktoken 
from askdoc.utils.vectorCreate import generate_embeddings
from askdoc.utils.pdfImageProcess import extract_pdf_content_and_images as extract_images
from askdoc.utils.commonUtils import identify_file_type,excel_to_markdown
from docx2pdf import convert
from paddleocr import PaddleOCR




def log_to_file(process_id):
    # 配置日志记录器
    logging.basicConfig(filename=f'process_{process_id}.log', level=logging.INFO, format='%(asctime)s - %(name)s - %(message)s')
    logger = logging.getLogger(__name__)
    return  logger
# def extract_pdf_content_stream(file_stream,tag=""):
#     pdf_data = []
#
#     pdf_reader = PyPDF2.PdfReader(file_stream)
#
#     for page_num in range(len(pdf_reader.pages)):
#         page = pdf_reader.pages[page_num]
#         page_content = page.extract_text().strip()
#         line = {'id': str(generate_random_string()), 'content': page_content, "tag": "   来源:"+str(tag)+" 第"+str(page_num)+"页"}
#         pdf_data.append(line)
#     return pdf_data
# def ocr():
#     ocr = PaddleOCR(use_angle_cls=True)
#     # ocr = PaddleOCR(use_angle_cls=True)
#     # img_path='D:\\python_projects\\paddleocr\\output_image.jpg'
#     img_path = 'C:\\a2.png'
#     # 读取图像并识别文本
#
#     result = ocr.ocr(img_path, cls=False)
#     txts = [line[1][0] for line in result[0]]
def extract_pdf_content_and_images(pdf_file_path, tag="",max_tokens=1024,filename='1',ct='0',et='0',fileid='1',folder_id='test1'):
    return extract_images(pdf_file_path,tag,max_tokens,filename,ct,et,fileid,folder_id)

def extract_pdf_content(pdf_file_path):
    pdf_data = []

    with open(pdf_file_path, 'rb') as pdf_file:
        pdf_reader = PyPDF2.PdfReader(pdf_file)

        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            page_content = page.extract_text().strip()
            line={'id':str(generate_random_string()),'content':page_content}

            pdf_data.append(line)
    return pdf_data
def generate_random_string(length=26):
    characters = string.ascii_lowercase
    random_string = ''.join(random.choice(characters) for _ in range(length))


    # timestamp = time.time()
    random_string_with_timestamp = random_string
    return random_string_with_timestamp


def save_to_json(data, json_file_path):
    os.makedirs(os.path.dirname(json_file_path), exist_ok=True)
    with open(json_file_path, 'w', encoding='utf-8') as json_file:
        json.dump(data, json_file, ensure_ascii=False, indent=4)


def reader_pdf(pdf_file_path,filename='0',ct='0',et='0',fileid='1',folder_id='test1',indexName=111):
   #print(111)
    if(indexName==111):
        indexName = generate_random_string()
    # pdf_data = extract_pdf_content(pdf_file_path)
    #print("asdasdasd")
    pdf_data = extract_pdf_content_and_images(pdf_file_path,tag="",max_tokens=1024,filename=filename,ct=ct,et=et,fileid=fileid,folder_id=folder_id)
    #print("pdf_data:", pdf_data)
    logger.debug(f'saved pdfdata:{pdf_data}')
    save_to_json(pdf_data, "data/jsonfile_{}.json".format(indexName))
    logger.debug('indexName is {}'.format(indexName))
    return  indexName

def read_image(file_path, indexName=111, r='1', t='0', t2='0', i='1'):
    ocr = PaddleOCR(use_angle_cls=False)
    max_tokens=512
    with open(file_path, "rb") as image_file:
        image_bytes = image_file.read()

    # 使用OCR工具处理图像字节流
    ocr_results = ocr.ocr(image_bytes)

    # 处理OCR结果
    if ocr_results:
        text_content=""
        for result in ocr_results:
            # 处理每个识别结果
            for detection in result:
                bbox, text_confidence_tuple = detection
                text, confidence = text_confidence_tuple
                # print("--------------------------------------------------------------")
                # print("Line result:", detection)
                # print("Extracted text:", text)
                # print("Confidence score:", confidence)
                text_content+=text
        
        # 按照 tokens 分割文本
        pages_result = []
        temp_text = ""
        for char_idx,char in enumerate(text_content):
            temp_text += char
            if num_tokens_from_messages_excel(temp_text) > max_tokens:
                # 当达到最大标记数时，将暂存的位置信息作为该文本片段的位置信息
                pages_result.append({'id': generate_random_string(), 'content': temp_text[:-1], 'tag': "来源："+r+" ", 'filename': r, 'starttime': t, 'endtime': t2, 'idfile': i})
                temp_text = char

        remaining_text = temp_text

        if remaining_text:
            pages_result.append({'id': generate_random_string(), 'content': remaining_text, 'tag': "来源："+r+" ", 'filename': r, 'starttime': t, 'endtime': t2, 'idfile': i})

        save_to_json(pages_result, "data/jsonfile_{}.json".format(indexName))
        print('indexName is {}'.format(indexName))
        return  indexName

    else:
        print("No text detected in the image.")

def read_images(file_path, filename='1', ct='0', et='0', fileid='1', indexName=111,folder_id='test1'):
    if(indexName==111):
        indexName = generate_random_string()
    ocr = PaddleOCR(use_angle_cls=False)
    max_tokens=512
    with open(file_path, "rb") as image_file:
        image_bytes = image_file.read()

    # 使用OCR工具处理图像字节流
    ocr_results = ocr.ocr(image_bytes)

    # 处理OCR结果
    if ocr_results:
        text_content=""
        for result in ocr_results:
            # 处理每个识别结果
            for detection in result:
                bbox, text_confidence_tuple = detection
                text, confidence = text_confidence_tuple
                # print("--------------------------------------------------------------")
                # print("Line result:", detection)
                # print("Extracted text:", text)
                # print("Confidence score:", confidence)
                text_content+=text
        
        # 按照 tokens 分割文本
        pages_result = []
        temp_text = ""
        overlap_ratio = 0.25
        for char_idx,char in enumerate(text_content):
            temp_text += char
            if num_tokens_from_messages_excel(temp_text) > max_tokens:
                overlap_length = int(len(temp_text) * overlap_ratio)
                overlap_text = temp_text[-overlap_length:]
                # 当达到最大标记数时，将暂存的位置信息作为该文本片段的位置信息
                pages_result.append({
                    'id': generate_random_string(), 
                    'content': temp_text[:-overlap_length], 
                    'tag': "来源："+filename+" ", 
                    'filename': filename, 
                    'starttime': ct, 
                    'endtime': et, 
                    'idfile': fileid,
                    'folder_id': folder_id,
                    'filetype': identify_file_type(filename)
                })
                # temp_text = char
                temp_text = overlap_text

        remaining_text = temp_text

        if remaining_text:
            pages_result.append({'id': generate_random_string(), 'content': remaining_text, 'tag': "来源："+filename+" ", 'filename': filename, 'starttime': ct, 'endtime': et, 'idfile': fileid,'folder_id': folder_id, 'filetype': identify_file_type(filename)})

        save_to_json(pages_result, "data/jsonfile_{}.json".format(indexName))
        print('indexName is {}'.format(indexName))
        return  indexName

    else:
        print("No text detected in the image.")

def read_excel2markdown(file_path, filename='1', ct='0', et='0', fileid='1', indexName=111,folder_id='test1'):
    if(indexName==111):
        indexName = generate_random_string()
    max_tokens=512
    output_dir = "data"

    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} does not exist.")
        return

    # 检查并创建保存目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    markdown_content = excel_to_markdown(file_path)

    pages_result = []

    # 遍历每个 sheet 的内容
    for sheet_name, content in markdown_content.items():
        temp_text = ""
        split_list = content.split('\n')

        if  not split_list:
            continue
            
        # # 第一行是标题行
        # headers = split_list[0]
        # datarows = split_list[1:]
        # 开始包含标题行
        # temp_text = headers + '\n' 
        temp_text = ''

        # 按字符遍历内容
        # for row in datarows:
        for row in split_list:
            temp_text += row +'\n'

            # 如果超过最大字符长度
            if num_tokens_from_messages_excel(temp_text) >= max_tokens:
                pages_result.append({
                    'id': generate_random_string(),
                    'content': temp_text.strip(),
                    'tag': f"来源：{filename} (Sheet: {sheet_name})",  # 添加 sheet 名信息
                    'filename': filename,
                    'starttime': ct,
                    'endtime': et,
                    'idfile': fileid,
                    'folder_id': folder_id,
                    'filetype': identify_file_type(filename)
                })
                #temp_text = headers + '\n'   # 重新开始新块
                temp_text = ''

        # 处理剩余未分割的文本
        if temp_text.strip():
            pages_result.append({
                'id': generate_random_string(),
                'content': temp_text.strip(),
                'tag': f"来源：{filename} (Sheet: {sheet_name})",  # 添加 sheet 名信息
                'filename': filename,
                'starttime': ct,
                'endtime': et,
                'idfile': fileid,
                'folder_id': folder_id,
                'filetype': identify_file_type(filename)
            })

    # 保存结果到 JSON 文件
    save_to_json(pages_result, f"data/jsonfile_{indexName}.json")
    print(f"IndexName is {indexName}")
    return indexName

def read_excel(file_path, filename='1', ct='0', et='0', fileid='1', indexName=111, folder_id='test1'):
    if indexName == 111:
        indexName = generate_random_string()
    max_tokens = 512
    output_dir = "data"

    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} does not exist.")
        return

    # 检查并创建保存目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    print('--------开始处理Excel文件的多个Sheet-----------------')
    try:
        # 读取Excel文件的所有Sheet
        xls = pd.ExcelFile(file_path)
        all_sheets_results = []

        for sheet_name in xls.sheet_names:
            print(f'处理Sheet: {sheet_name}')
            # 读取每个Sheet的数据
            sheet_data = pd.read_excel(xls, sheet_name=sheet_name)

            # 检查是否为空Sheet
            if sheet_data.empty:
                print(f"Warning: Sheet {sheet_name} is empty. Skipping.")
                continue

            text_content = ''.join([str(cell) for row in sheet_data.iterrows() for cell in row[1] if pd.notna(cell)])
            text_positions = [(row_idx + 1, col_idx + 1) for row_idx, row in sheet_data.iterrows() for col_idx, cell in enumerate(row) if pd.notna(cell)]

            pages_result = []
            temp_text = ""
            for char_idx, char in enumerate(text_content):
                temp_text += char
                if num_tokens_from_messages_csv(temp_text) > max_tokens:
                    temp_tag = text_positions[char_idx] if char_idx < len(text_positions) else None
                    pages_result.append({
                        'id': generate_random_string(),
                        'content': temp_text[:-1],
                        'tag': f"来源：{filename} Sheet:{sheet_name}",
                        'filename': filename,
                        'starttime': ct,
                        'endtime': et,
                        'idfile': fileid,
                        'folder_id': folder_id,
                        'filetype': identify_file_type(filename)
                    })
                    temp_text = char

            # 处理剩余的文本
            if temp_text:
                last_position = text_positions[-1] if text_positions else None
                pages_result.append({
                    'id': generate_random_string(),
                    'content': temp_text,
                    'tag': f"来源：{filename} Sheet:{sheet_name}",
                    'filename': filename,
                    'starttime': ct,
                    'endtime': et,
                    'idfile': fileid,
                    'folder_id': folder_id,
                    'filetype': identify_file_type(filename)
                })

            # 保存每个Sheet的结果到all_sheets_results
            all_sheets_results = all_sheets_results + pages_result
                
        try:
            json_file_path = os.path.join(output_dir, f"jsonfile_{indexName}.json")
            save_to_json(all_sheets_results, json_file_path)
            print(f"Successfully saved {json_file_path}")
        except Exception as save_error:
            print(f"Error saving JSON for {index_name}: {save_error}")
        print(f'indexName is {indexName}')
        return indexName

    except Exception as e:
        traceback.print_exc()
        print("Error processing Excel file:", str(e))


def read_qa(qa, filename='1', ct='0', et='0', fileid='1', indexName=111,folder_id='test1'):
    if(indexName==111):
        indexName = generate_random_string()
    max_tokens=512
    print('--------开始处理qa-----------------')
    try:
        text_content = qa  # 用于存储文件内容的字符
        # 按照 tokens 分割文本
        pages_result = []
        temp_text = ""
        for char in text_content:
            temp_text += char
            if num_tokens_from_messages_excel(temp_text) > max_tokens:
                pages_result.append({'id': generate_random_string(), 'content': temp_text[:-1], 'tag': "来源："+filename+" ", 'filename': filename, 'starttime': ct, 'endtime': et, 'idfile': fileid, 'folder_id':folder_id,'filetype':identify_file_type(filename)})
                temp_text = char
        remaining_text = temp_text
        if remaining_text:
            pages_result.append({'id': generate_random_string(), 'content': remaining_text, 'tag':"来源："+ filename+" ", 'filename': filename, 'starttime': ct, 'endtime': et, 'idfile': fileid, 'folder_id':folder_id, 'filetype':identify_file_type(filename)})
        save_to_json(pages_result, "data/jsonfile_{}.json".format(indexName))
        print('indexName is {}'.format(indexName))
        return indexName

    except Exception as e:
        traceback.print_exc()
        print("Error reading text file:", str(e))

def read_csv(file_path, filename='1', ct='0', et='0', fileid='1', indexName=111,folder_id='test1'):
    if(indexName==111):
        indexName = generate_random_string()
    max_tokens=512
    print('--------开始处理CSV文件-----------------')
    try:
        # 尝试使用不同的编码格式进行解码
        encodings = ['utf-8', 'gbk', 'utf-16']
        for encoding in encodings:
            try:
                with open(file_path, 'r', newline='', encoding=encoding) as csvfile:
                    reader = csv.reader(csvfile)
                    # 用于存储提取出的文本内容和每个文本片段的位置信息
                    text_content = ''
                    text_positions = []  # 用于存储每个文本片段的位置信息

                    for row_idx, row in enumerate(reader, start=1):
                        for col_idx, cell in enumerate(row, start=1):
                            if cell is not None:
                                if isinstance(cell, str):
                                    text_content += cell
                                    text_positions.append((row_idx, col_idx))
                                elif isinstance(cell, (int, float)):
                                    text_content += str(cell)
                                    text_positions.append((row_idx, col_idx))

                    # 按照 tokens 分割文本
                    pages_result = []
                    temp_text = ""
                    temp_tag = ""  # 用于暂存当前文本片段的位置信息
                    for char_idx, char in enumerate(text_content):
                        temp_text += char
                        if num_tokens_from_messages_csv(temp_text) > max_tokens:
                            if char_idx < len(text_positions):
                                temp_tag = text_positions[char_idx]
                            else:
                                temp_tag = None

                            pages_result.append({'id': generate_random_string(), 'content': temp_text[:-1], 'tag': "来源："+filename+" ", 'filename': filename, 'starttime': ct, 'endtime': et, 'idfile': fileid, 'folder_id': folder_id,'filetype':identify_file_type(filename)})
                            temp_text = char
                        else:
                            if char_idx < len(text_positions):
                                temp_tag = text_positions[char_idx]
                            else:
                                temp_tag = None

                    remaining_text = temp_text

                    if remaining_text:
                        last_position = text_positions[-1]
                        pages_result.append({'id': generate_random_string(), 'content': remaining_text, 'tag': "来源："+filename+" ", 'filename': filename, 'starttime': ct, 'endtime': et, 'idfile': fileid,'folder_id': folder_id, 'filetype':identify_file_type(filename)})

                    # 保存到 JSON 文件
                    save_to_json(pages_result, "data/jsonfile_{}.json".format(indexName))
                    print('indexName is {}'.format(indexName))
                    return indexName
            except UnicodeDecodeError:
                continue

    except Exception as e:
        traceback.print_exc()
        print("Error processing CSV file:", str(e))

def num_tokens_from_messages_csv(text):
    return len(text.split())

def read_txt(file_path, filename='1', ct='0', et='0', fileid='1', indexName=111,folder_id='test1'):
    if(indexName==111):
        indexName = generate_random_string()
    max_tokens=512

    print('--------开始处理TXT文件-----------------')
    try:
        content = ''  # 用于存储文件内容的字符串
        with open(file_path, 'r', encoding='utf-8') as file:
            line = file.readline()  # 逐行读取文件内容
            while line:
                content += line.rstrip('\n')  # 移除行末的换行符并拼接到 content 中
                line = file.readline()  # 继续读取下一行内容
        
        text_content=content
        # 按照 tokens 分割文本
        pages_result = []
        temp_text = ""
        for char in text_content:
            temp_text += char
            if num_tokens_from_messages_excel(temp_text) > max_tokens:
                pages_result.append({'id': generate_random_string(), 'content': temp_text[:-1], 'tag': "来源："+filename+" ", 'filename': filename, 'starttime': ct, 'endtime': et, 'idfile': fileid,'folder_id': folder_id,'filetype':identify_file_type(filename)})
                temp_text = char
        remaining_text = temp_text
        if remaining_text:
            pages_result.append({'id': generate_random_string(), 'content': remaining_text, 'tag':"来源："+ filename+" ", 'filename': filename, 'starttime': ct, 'endtime': et, 'idfile': fileid, 'folder_id': folder_id,'filetype':identify_file_type(filename)})
        save_to_json(pages_result, "data/jsonfile_{}.json".format(indexName))
        print('indexName is {}'.format(indexName))
        return indexName

    except Exception as e:
        traceback.print_exc()
        print("Error reading text file:", str(e))

def read_ppt(ppt_file, filename='1', ct='0', et='0', fileid='1', indexName=111,folder_id='test1'):
    if(indexName==111):
        indexName = generate_random_string()
    prs = Presentation(ppt_file)
    ocr = PaddleOCR(use_angle_cls=False)
    max_tokens=512
    text_content = ""
    print(11111)
    #遍历每个幻灯片
    for slide in prs.slides:
        for shape in slide.shapes:
            # 提取文本框内容
            if hasattr(shape, "text"):
                text_content += shape.text + "\n"
            # 提取图片内容
            try:
                if hasattr(shape, "image"):
                    image = shape.image
                    image_bytes = image.blob
                    logger.debug(f'')
                    ocr_results = ocr.ocr(image_bytes)
                    if ocr_results:
                        for result in ocr_results:
                            if result:
                            # 处理每个识别结果
                                for detection in result:
                                    bbox, text_confidence_tuple = detection
                                    text, confidence = text_confidence_tuple
                                    # print("--------------------------------------------------------------")
                                    # print("Line result:", detection)
                                    # print("Extracted text:", text)
                                    # print("Confidence score:", confidence)
                                    text_content+=text
                        text_content+="\n"
            except Exception as e:
                logger.warning(f'Error processing ppt image: {e}, skip')
            # 提取表格内容
            try:
                if hasattr(shape, "chart"):
                    table = shape.table
                    for row in table.rows:
                        for cell in row.cells:
                            table_content += cell.text
                        table_content += "\n"
            except Exception as e:
                logger.warning(f'Error processing ppt table: {e}, skip')

    # 按照 tokens 分割文本
    # 不知道为什么这里写的计算token的时候一个一个字符地计算，也就是一个字母字母地计算，100来个字母就到达512token了。可能是我
    # 这里sdk版本和客户环境不一样？加了字符串判断，不影响本来的逻辑。
    logger.debug(f'text_content:{text_content},type:{type(text_content)}')
    if type(text_content) == str:
        text_content = [i+'\n' for i in text_content.split('\n')]
    pages_result = []
    temp_text = ""
    for char_idx,char in enumerate(text_content):
        logger.debug(f'char_idx:{char_idx}, char:{char}')
        temp_text += char
        if num_tokens_from_messages_excel(temp_text) > max_tokens:
            # 当达到最大标记数时，将暂存的位置信息作为该文本片段的位置信息
            pages_result.append({'id': generate_random_string(), 'content': temp_text[:-1], 'tag': filename,'filename': filename, 'starttime': ct, 'endtime': et, 'idfile': fileid, 'folder_id': folder_id,'filetype':identify_file_type(filename)})
            logger.debug(f'reached maxtoken, content:{temp_text[:-1]}')
            temp_text = char

    remaining_text = temp_text

    if remaining_text:
        pages_result.append({'id': generate_random_string(), 'content': remaining_text, 'tag': filename,'filename': filename, 'starttime': ct, 'endtime': et, 'idfile': fileid, 'folder_id': folder_id,'filetype':identify_file_type(filename)})

    save_to_json(pages_result, "data/jsonfile_{}.json".format(indexName))
    print('indexName is {}'.format(indexName))
    return  indexName


def convert_to_pdf(input_docx, output_pdf):
    convert(input_docx, output_pdf)


def num_tokens_from_messages_excel(messages):
    encoding = tiktoken.get_encoding("cl100k_base")
    num_tokens = 0
    if type(messages) == str:
        num_tokens += 4  # 每条消息的开头和结尾
        num_tokens += len(encoding.encode(messages))
    else: 
        for message in messages:
            num_tokens += 4  # 每条消息的开头和结尾
            num_tokens += len(encoding.encode(message))
    num_tokens += 2  # 每个回复以<im_start>assistant开始
    return num_tokens

def num_tokens_from_messages_csv(messages):
    encoding = tiktoken.get_encoding("cl100k_base")
    num_tokens = 0
    for message in messages:
        num_tokens += 4  # 每条消息的开头和结尾
        num_tokens += len(encoding.encode(message))
    num_tokens += 2  # 每个回复以<im_start>assistant开始
    return num_tokens

def   readPdfAndUpload(stream,tag="",max_tokens=512):
    #1. 抽取内容 内容/每一页
    pdf_data = extract_pdf_content_and_images(stream,tag,max_tokens)
    #2.  向量化

    # 定义一个函数，用于处理单个pdf_data项
    def process_pdf_data(item):

        content = item['content']

        content_embeddings = generate_embeddings(content)

        item['contentVector'] = content_embeddings
        #del item['tag']

    # 创建线程池
    with cf.ThreadPoolExecutor() as executor:
        # 使用线程池并行处理pdf_data中的每个项
        futures = [executor.submit(process_pdf_data, item) for item in pdf_data]

    # 等待所有任务完成并获取结果
    cf.wait(futures)
    #print(pdf_data[0])
    return pdf_data

def createDocument(content,indexName,conversationId):
    documentId=str(indexName)+'==='+str(conversationId)+"==="
    print(f'文档内容....{content}')
    content_embeddings = generate_embeddings(content)

    document={'id':documentId ,'contentVector':content_embeddings ,'content': content+" 来源:用户提供"}

    #print(f"Uploaded {len(1)} documents")
    return  document

if __name__ == "__main__":
    # pdf_file_path ='c:\脉搏传感器说明书.pdf'
    # json_file_path = "output112.json"
    #
    # pdf_data = extract_pdf_content(pdf_file_path)
    # save_to_json(pdf_data, json_file_path)
    length_of_random_string = 26
    random_string = generate_random_string(length_of_random_string)
    print(random_string)