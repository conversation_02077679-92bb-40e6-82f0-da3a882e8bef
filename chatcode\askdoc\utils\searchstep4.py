import os
import sys
import json
import logging
import traceback
logger=logging.getLogger(__name__)
handler=logging.StreamHandler()
handler.setFormatter(logging.Formatter('[%(asctime)s] [%(levelname)s] %(filename)s:%(lineno)d - %(message)s'))
logger.addHandler(handler)
logger.setLevel(logging.DEBUG)
import tiktoken
from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential

# from dotenv import load_dotenv

from askdoc.utils.vectorCreate import generate_embeddings
import datetime
sys.setrecursionlimit(200000)

# env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env')
# load_dotenv(env_path)
from askdoc.config import *

# Load configuration from config.json
CONFIG_DIR = os.path.join(os.path.dirname(__file__), '../config.json')
with open(CONFIG_DIR, 'r') as f:
    config = json.load(f)
service_endpoint = config['main_rag_pipeline_config']['search_api_endpoint']
azure_key = config['main_rag_pipeline_config']['search_api_key']
index_name = config['main_rag_pipeline_config']['search_api_index_name']

credential = AzureKeyCredential(azure_key)
# Pure Vector Search
query = "HK-2000C 集成化数字脉搏传感器的技术指标是什么"


import openai
openai.api_type = "azure"
openai.api_key= '********************************' 
openai.api_base = 'https://af-eastus-aoai.openai.azure.com/' 
openai.api_version ="2024-02-01"


def filter_filename(query:str,filenamelist:list[dict])->list[dict]:
    """让gpt筛选文件名"""
    logger.info('开始筛选文件名')

    supposed_format='''
    {"selected_document": [fileid1, fileid2,]}
    '''

    response_format={ "type": "json_object" }
    messages=[
        {'role': 'system',
        'content': ('请你扮演一个文档筛选机器人，用户会给你一个请求和搜索到的文档的名字，'
                    '请你思考问题是否可能与列表中文档有关系，筛选出有可能包含答案的文档.你的回答应以json格式给出.json格式'
                    f'为：{supposed_format}. for example: '
                    '问题：乳粉的感官要求是什么？ 待过滤文档：[{"filename":"乳粉国家标准"，"fileid":"12258","content":document_content}'
                    ',{"filename":"乳粉中污染物含量要求.pdf","fileid":"27736","conetent":document_content}].你应输出：'
                    '{"selected_document": [{"filename":"乳粉国家标准"，"fileid":"12258"}]}.文档的数量没有限制'
                    )
        },
        {'role': 'user', 'content': f'请求：{query} 搜索到的文档的名字： {filenamelist}'},
    ]


    response=openai.ChatCompletion.create(
        engine='4o-mini',
        messages= messages,
        response_format=response_format,
    )

    result=json.loads(response.choices[0].message.content)["selected_document"]
    logger.info('过滤后的文件：\n%s', chr(10).join([str(i) for i in result]))
    return result


def is_valid_file_type(file_name):
    support_file_type = ['pdf','xlsx','xls','png','jpg','jpeg','csv','txt','doc','docx']
    extension=file_name.split(".")[-1].lower()
    if extension not in support_file_type:
        return False
    else:
        return True


def num_tokens_from_messages(message):
    num_tokens=0
    encoding = tiktoken.get_encoding("cl100k_base")
    for key, value in message.items():
        if key == "content":  # if there's a name, the role is omitted
            num_tokens += len(encoding.encode(value))  # role is always required and always 1 token
    #print(num_tokens)
    return num_tokens


def searchIndex_filename(query,index_name,*,folder_id_list=[])->list[dict]:
    '''
    以关键字搜索文件名，返回列表,列表包括文件名和文件id
    '''
    logger.info(f'开始搜索文件名')
    search_client = SearchClient(service_endpoint, index_name, credential=credential)


    final_filter=''
    filters_folders=[]
    for fold_id in folder_id_list:
        filters_folders.append(f"folder_id eq '{fold_id}'")
    filter_query_folder=' or '.join(filters_folders)
    final_filter+='('+filter_query_folder+')' if filter_query_folder!='' else ''

    current_time=datetime.datetime.now()
    filter_expiretime=f"((endtime gt '{current_time.strftime('%Y-%m-%d %H:%M:%S')}') or (endtime eq ''))"
    if final_filter : final_filter=final_filter+' and '+filter_expiretime
    else: final_filter=filter_expiretime
    logger.info(f'使用的过滤条件:\n {final_filter}')


    top=50
    count=0
    file_list=[]
    filename_list=[]
    qa_list=[]
    while True:
        results = search_client.search(
            search_text=query,
            select=['id','idfile','filename','endtime','folder_id','starttime','content'],
            top=top,
            skip=count*top,
            filter=final_filter+' and filetype eq \'pdf\'',
            query_type="semantic", query_language="zh-cn", semantic_configuration_name='my-semantic-config',
        )
        count+=1
        logger.info(f'正在查看第{count}页搜索结果')


        for i in results:
            item={'filename':i['filename'],'fileid':i['idfile'],'content':i['content']}
            # logger.info(f'文件信息：{item}')
            logger.debug(f'文件详细信息：{i}')

            if is_valid_file_type(item['filename']):
                if item['filename'] not in filename_list:
                    file_list.append(item)
                    filename_list.append(item['filename'])
                    logger.debug(f'{item["filename"]}已添加到文件列表')
                else:
                    logger.debug(f'{item["filename"]}已存在于文件列表')
            else:
                if item not in qa_list:
                    qa_list.append(item)
                    logger.debug(f'{item["filename"]}已添加到问答列表')
                else:
                    logger.debug(f'{item["filename"]}已存在于问答列表')

        for filename in filename_list:print(filename)
        input('input to continue')
        
        if len(file_list)>=5 and len(qa_list)>=0 or count >= 10:
            break
        else:
            logger.info(f'文件列表长度：{len(file_list)},问答列表长度：{len(qa_list)}')
    result=file_list[:5]+qa_list[:5]
    
    logger.info(f'搜索到的相关文件:\n{chr(10).join([i["filename"]+"  "+i["fileid"] for i in result])}')
    logger.info(f'文件名搜索完成')
    return result


def searchIndex(query,index_name,idlist=[],folder_id_list=[],filename_list=[],core='gpt-4-1106-preview'):
    '''
    通过传入的query搜索，并使用idlist，folder_id_list,filename_list三个过滤条件来过滤。如果不传入某个参数或传入空列表，则不使用该过滤条件。
    如：

    query='白酒',
    index_name='sgsaf',
    idlist=[],
    folder_id_list=['测试语料','测试语料2','test1'],
    filename_list=[]

    对应的最终查询语句为：

    (folder_id eq '测试语料' or folder_id eq '测试语料2' or folder_id eq 'test1') and ((endtime gt '2024-08-02 14:52:15') or (endtime eq ''))

    即idlist和filename_list没有加入到过滤条件中,只使用了folder_id_list和expiretime作为过滤条件。过期时间的过滤条件是默认的。
    '''
    search_client = SearchClient(service_endpoint, index_name, credential=credential)
    current_time=datetime.datetime.now()


    filter_fileid=' or '.join([f"idfile eq '{i}'" for i in idlist])
    filter_folderid=' or '.join([f"folder_id eq '{i}'" for i in folder_id_list])
    filter_filename=' or '.join([f"filename eq '{i}'" for i in filename_list])
    current_time=datetime.datetime.now()
    filter_expiretime=f"(endtime gt '{current_time.strftime('%Y-%m-%d %H:%M:%S')}') or (endtime eq '')"
    logger.debug(f'your fileid filter is {filter_fileid}')
    logger.debug(f'your folder fileter is {filter_folderid}')
    logger.debug(f'your filenmae fileter is {filter_filename}')
    logger.debug(f'your expiretime filter is {filter_expiretime}')

    final_filter_list=[]
    final_filter_list.append(filter_fileid)
    final_filter_list.append(filter_folderid)
    final_filter_list.append(filter_filename)
    final_filter_list.append(filter_expiretime)

    first_flag=True
    final_filter=''
    for i in final_filter_list:
        if i == '':
            continue
        else:
            if first_flag:
                first_flag=False
                final_filter+=f'({i})'
            else :
                final_filter+=f' and ({i})'
    logger.info(f'过滤条件: \n{final_filter}')
    # BATCH_SIZE = 500
    
    # # 计算需要的批次数量
    # num_batches = (len(idlist) + BATCH_SIZE - 1) // BATCH_SIZE
    vector,embeddings_model,embeddings_tokens=generate_embeddings(query)
        

    results = search_client.search(
        search_text=query,
        # update to vector_query fileds 放在query里了，
        vector=vector,
        #vector_queries = [vector_query],
        top_k=2,
        vector_fields="contentVector",
        query_type="semantic", query_language="zh-cn", semantic_configuration_name='my-semantic-config',
        # query_caption="extractive", query_answer="extractive",
        select=["content","tag",'idfile'],
        filter=final_filter,
    )

    i=0
    contents=[]
    num_tokens=0
    max_token=12000
    if(core=='gpt16'):
        max_token=12000

    sum=0
    # logger.debug(f'results:{results}')

    for result in results:
        sum=sum+1
        if(sum>3):
            break
        tknum=num_tokens_from_messages(result)
        # print('token 数：',tknum)
        i+=1
        num_tokens=num_tokens+tknum
        if num_tokens > max_token:
            break
        contents.append(result)
    logger.debug(f'搜索到的排名前{i}的chunk信息:\n{chr(10).join([str(i) for i in contents])}')
    return contents,embeddings_model,embeddings_tokens


def update_endtime(folder_id_list:list[str],file_id_list:list[str], endtime:str):
    '''
    folder_id_list: 文件夹路径
    file_id_list: 文件名
    filename_list: 文件名
    endtime: 失效时间
    '''
    index_name = 'sgsaf'
    logger.debug(f'updating endtime to {endtime}')
    search_client = SearchClient(service_endpoint, index_name, AzureKeyCredential(azure_key))
    filter_folder_id = ' or '.join([f"folder_id eq \'{i}\'" for i in folder_id_list])
    filter_file_id = ' or '.join([f"idfile eq \'{i}\'" for i in file_id_list])
    # filter_filename = ' or '.join([f"filename eq \'{i}\'" for i in filename_list])
    filter_list=[]
    filter_list.append(filter_folder_id)
    # filter_list.append(filter_filename)
    filter_list.append(filter_file_id)
    logger.debug(f'your filter_list is:')
    [print(i) for i in filter_list]

    filter=''
    first_flag=-1
    for num,i in enumerate(filter_list):
        if i == '':
            continue
        else:
            if first_flag == -1:
                filter = i
                first_flag=0
            else:
                filter = f'{filter} and {i}'

    logger.debug(f'your filter is {filter}')

    results = search_client.search(
        search_text="*", 
        filter=filter, 
        select=[
        "endtime","tag", "id","folder_id"
        ,"starttime","idfile",'filename','content'],
        )

    id_list=[]
    for num,result in enumerate(results):
        id_list.append(result['id'])
    logger.debug(f'search completed, got {len(id_list)} results')

    documents_to_udpate=[
        {
            'id': f'{i}',
            'endtime': endtime
        } for i in id_list
    ]

    try:
        response = search_client.merge_documents(documents=documents_to_udpate)
        for num,result in enumerate(response):
            if result.status_code != 200:
                logger.error(f'error in merge_documents: {result}')
                return -1
            else:
                logger.debug(f'updated all documents successfully')
                return 0
    except Exception as e:
        # 输出traceback信息
        traceback.print_exc()
        logger.error(f'error in merge_documents: {e}')
        return -1


def searchSingleFile(query,id,folder_id_list):
    '''
    限定执行向量搜索. query是用户询问内容，id是文件id,folder_id_list是用户能访问的文件夹id列表。
    '''
    content,embeddings_model,embeddings_token=searchIndex(query,'sgsaf',idlist=[id],folder_id_list=folder_id_list)
    logger.debug(f'搜索到{len(content)}个chunk,内容为：{chr(10).join([str(i) for i in content])}')
    return content,embeddings_model,embeddings_token


def search_related_content(keywords:list[str],query:str,folder_id_list:list[str]=[]):
    '''
    根据用户输入的query和关键字，搜索数据库中与用户query相关内容。
    '''
    filename_list=searchIndex_filename(query,'sgsaf',folder_id_list=folder_id_list)
    filename_list_filtered=filter_filename(query,filename_list)
    if len(filename_list_filtered)==0:
        return [],'large',0

    contents=[]
    embeddings_tokens=0
    logger.info(f'开始逐个文件搜索,共{len(filename_list_filtered)}个文件.若超过5个只搜索前5个文件')
    for i,file_info in enumerate(filename_list_filtered,start=1):
        if i>3:break #只找前五的文件
        logger.info(f'正在搜索第{i}个文件：{file_info["filename"]}')
        content,embeddings_model,embeddings_token=searchIndex(query,'sgsaf',idlist=[file_info['fileid']],folder_id_list=folder_id_list,filename_list=[])
        contents=contents+content
        embeddings_tokens=embeddings_tokens+embeddings_token
    logger.info(f'搜索完成，共搜索到{len(contents)}个chunk') 

    return contents,embeddings_model,embeddings_tokens


