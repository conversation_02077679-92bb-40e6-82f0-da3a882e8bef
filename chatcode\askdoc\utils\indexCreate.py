import os
import json
import logging
logger=logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
import openai
from azure.search.documents.indexes import SearchIndexClient

from azure.core.credentials import AzureKeyCredential
from azure.search.documents.indexes.models import (
    SearchIndex,
    SearchField,
    SearchFieldDataType,
    SimpleField,
    SearchableField,
    SearchIndex,
    SemanticConfiguration,
    PrioritizedFields,
    SemanticField,
    SearchField,
    SemanticSettings,
    VectorSearch,
    VectorSearchAlgorithmConfiguration,
)

from askdoc.utils.fileUtils import print_log

CONFIG_DIR = os.path.join(os.path.dirname(__file__), '../config.json')
with open(CONFIG_DIR, 'r') as f:
    config = json.load(f)
service_endpoint = config['main_rag_pipeline_config']['search_api_endpoint']
azure_key = config['main_rag_pipeline_config']['search_api_key']
openai.api_type = "azure"
openai.api_key = config['main_rag_pipeline_config']['azure_openai_api_key']
openai.api_base = config['main_rag_pipeline_config']['azure_openai_api_base']
openai.api_version = config['main_rag_pipeline_config']['azure_openai_api_version']

print_log('key: ', azure_key)
credential = AzureKeyCredential(azure_key)
# Create a search index
index_client = SearchIndexClient(
    endpoint=service_endpoint, credential=credential)
def  createIndex(index_name):
    return 0
    fields = [
        SimpleField(name="id", type=SearchFieldDataType.String, key=True, sortable=True, filterable=True, facetable=True),
        SearchableField(name="tag", type=SearchFieldDataType.String),
        SearchableField(name="content", type=SearchFieldDataType.String),
        SearchableField(name="filename", type=SearchFieldDataType.String,filterable=True),
        SearchableField(name="starttime", type=SearchFieldDataType.String),
        SearchableField(name="endtime", type=SearchFieldDataType.String, filterable=True),
        SearchableField(name="idfile", type=SearchFieldDataType.String,filterable=True),
        SearchableField(name="folder_id", type=SearchFieldDataType.String,filterable=True),
        #SearchableField(name="category", type=SearchFieldDataType.String,
        #                filterable=True),
        #SearchField(name="titleVector", type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
        #            searchable=True, vector_search_dimensions=1536, vector_search_configuration="my-vector-config"),
        SearchField(name="contentVector", type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                    searchable=True, vector_search_dimensions=3072, vector_search_configuration="my-vector-config"),
    ]

    vector_search = VectorSearch(
        algorithm_configurations=[
            VectorSearchAlgorithmConfiguration(
                name="my-vector-config",
                kind="hnsw",
                hnsw_parameters={
                    "m":10,
                    "efConstruction": 500,
                    "efSearch": 600,
                    "metric": "cosine"
                }
            )
        ]
    )

    semantic_config = SemanticConfiguration(
        name="my-semantic-config",
        prioritized_fields=PrioritizedFields(
            # title_field=SemanticField(field_name="title"),
            # prioritized_keywords_fields=[SemanticField(field_name="category")],
            prioritized_content_fields=[SemanticField(field_name="content")]
        )
    )

    # Create the semantic settings with the configuration
    semantic_settings = SemanticSettings(configurations=[semantic_config])

    # Create the search index with the semantic settings
    index = SearchIndex(name=index_name, fields=fields,
                        vector_search=vector_search, semantic_settings=semantic_settings)
    try:
      result = index_client.create_or_update_index(index)
      print(f' {result.name} created')
      logger.debug(f' {result.name} created')
    except Exception as e:
        logger.info(f'error:{e}')
        print('index exist!')

def delIndex(index_name):
    fields = [
        SimpleField(name="id", type=SearchFieldDataType.String, key=True, sortable=True, filterable=True,
                    facetable=True),
        # SearchableField(name="title", type=SearchFieldDataType.String),
        SearchableField(name="content", type=SearchFieldDataType.String),
        # SearchableField(name="category", type=SearchFieldDataType.String,
        #                filterable=True),
        # SearchField(name="titleVector", type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
        #            searchable=True, vector_search_dimensions=1536, vector_search_configuration="my-vector-config"),
        SearchField(name="contentVector", type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                    searchable=True, vector_search_dimensions=3072, vector_search_configuration="my-vector-config"),
    ]

    vector_search = VectorSearch(
        algorithm_configurations=[
            VectorSearchAlgorithmConfiguration(
                name="my-vector-config",
                kind="hnsw",
                hnsw_parameters={
                    "m": 4,
                    "efConstruction": 400,
                    "efSearch": 500,
                    "metric": "cosine"
                }
            )
        ]
    )

    semantic_config = SemanticConfiguration(
        name="my-semantic-config",
        prioritized_fields=PrioritizedFields(
            # title_field=SemanticField(field_name="title"),
            # prioritized_keywords_fields=[SemanticField(field_name="category")],
            prioritized_content_fields=[SemanticField(field_name="content")]
        )
    )

    # Create the semantic settings with the configuration
    semantic_settings = SemanticSettings(configurations=[semantic_config])

    # Create the search index with the semantic settings
    index = SearchIndex(name=index_name, fields=fields,
                        vector_search=vector_search, semantic_settings=semantic_settings)
    result = index_client.delete_index(index_name)
    print(result)
if __name__ == '__main__':
    delIndex('znihpoiztqzahjadurbw1691130105')

