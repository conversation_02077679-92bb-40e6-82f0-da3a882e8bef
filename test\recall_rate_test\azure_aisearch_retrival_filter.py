import sys
sys.path.append('./chatcode')
from askdoc.retrival.azure_aisearch_retrival import AzureAISearchRetrivalModule


if __name__ == "__main__":
    RetrivalModule = AzureAISearchRetrivalModule(
        endpoint="https://<your-search-service-name>.search.windows.net",
        key="<your-admin-api-key>",
        index_name="<your-index-name>"
    )

    filters = {
        "name": ["<PERSON>", "<PERSON>"],
        "age": [30,40],
        "gender": ["male","female"]
    }
    print(RetrivalModule.filter_list2filter(filters))